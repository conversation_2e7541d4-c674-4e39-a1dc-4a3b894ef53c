from flask import jsonify, g, request, current_app
import logging
import functools
import secrets
import time
import threading
from datetime import datetime, timedelta, timezone
import hashlib
import math
import re
import random
import string
import json
from werkzeug.exceptions import NotFound

# 第三方库导入
import pyotp
from flask import Blueprint, request, jsonify, g, current_app, render_template, redirect, url_for, flash, send_from_directory, abort, make_response, session
from flask_jwt_extended import create_access_token, jwt_required, get_jwt_identity
from werkzeug.security import generate_password_hash, check_password_hash
from sqlalchemy import func, and_, or_, not_
from sqlalchemy.exc import IntegrityError

# 项目内导入
from ..models import (
    db, User, Subscription, AdspowerAccount, 
    Device, PaymentRecord, Payment, LoginSession,
    SubscriptionType, SubscriptionInstance # Added SubscriptionInstance
)
from ..services import auth_service
from ..services.auth_service import AuthService, DeviceAuthService, TwoFactorAuthService
from ..services.payment_service import PaymentService # Import PaymentService
from ..services.device_service import DeviceService
from ..services.subscription_service import SubscriptionService
# from ..services.email_service import EmailService # Commented out EmailService import
from ..adspower_api import get_adspower_api
from ..webdriver_pool import get_account_driver_manager
from ..services.epay_service import EpayService # 导入 EpayService
from authlib.integrations.flask_client import OAuth # 在顶部导入
from ..utils import jsonify_response, get_real_ip # Import from ..utils
from ..decorators import login_required, admin_required # Import decorators

# 配置日志
logger = logging.getLogger(__name__)

# 导入蓝图实例
from . import api

# 初始化服务 - REMOVED Global Initialization
# auth_service = AuthService()
# device_auth_service = DeviceAuthService()
# two_factor_auth_service = TwoFactorAuthService()
# device_service = DeviceService()
# # email_service = EmailService() # Commented out EmailService initialization
# subscription_service = SubscriptionService()  # Add this import
# epay_service = EpayService() # 初始化 EpayService
# payment_service = PaymentService() # Instantiate PaymentService globally or within the route

# 初始化Authlib OAuth客户端
oauth_clients = OAuth() # Renamed as per previous, will be initialized in create_app

def init_oauth(current_app_instance): # Renamed current_app to current_app_instance to avoid conflict
    # global auth_service # No longer needed as global
    # Access auth_service via g or app context if needed, or pass it
    
    oauth_clients.init_app(current_app_instance)
    oauth_clients.register(
        name='keycloak',
        client_id=current_app_instance.config['OIDC_CLIENT_ID'],
        client_secret=current_app_instance.config['OIDC_CLIENT_SECRET'],
        server_metadata_url=f"{current_app_instance.config['OIDC_ISSUER_URL']}/.well-known/openid-configuration",
        client_kwargs={
            'scope': ' '.join(current_app_instance.config['OIDC_SCOPES']),
            'token_endpoint_auth_method': 'client_secret_post', # 或 client_secret_basic
        },
    )

# 辅助函数
def find_subscription_type(identifier):
    """通过 id 或 code 查找订阅类型
    
    Args:
        identifier: 可以是 int (id) 或 str (code)
    
    Returns:
        SubscriptionType 对象或 None
    """
    if identifier is None:
        return None
    
    # 如果是数字或数字字符串，优先作为 id 查询
    if isinstance(identifier, int) or (isinstance(identifier, str) and identifier.isdigit()):
        subscription_type = SubscriptionType.query.get(int(identifier))
        if subscription_type:
            return subscription_type
    
    # 作为 code 查询
    if isinstance(identifier, str):
        return SubscriptionType.query.filter_by(code=identifier).first()
    
    return None

# 用户认证相关路由
@api.route('/auth/register', methods=['POST'])
def register():
    """用户注册接口"""
    data = request.json
    email = data.get('email')
    password = data.get('password')
    # verification_code = data.get('verification_code') # Removed verification_code

    # 基本输入验证
    if not all([email, password]): # Removed verification_code from check
        logger.warning(f"[注册] 请求缺少必要字段: email={bool(email)}, password={bool(password)}") # Adjusted log message
        return jsonify_response(success=False, message='缺少必要字段 (邮箱, 密码)', data=None, status_code=400) # Adjusted error message

    # 邮箱格式验证 (简单的)
    if '@' not in email or '.' not in email.split('@')[-1]:
        logger.warning(f"[注册] 邮箱格式无效: {email}")
        return jsonify_response(success=False, message='邮箱格式无效', data=None, status_code=400)
        
    # 密码强度验证 (在 AuthService 中进行)

    # 获取AuthService实例 (Now from g)
    auth_service_instance = g.auth_service 

    # 调用注册服务
    user, message = auth_service_instance.register_user(email, password) # Removed verification_code from call
    
    if user:
        # 注册成功
        logger.info(f"[注册成功] 用户 {email} 注册成功，用户ID: {user.id}")
        # 可以在这里考虑是否直接返回登录令牌
        # token = auth_service.generate_token(user)
        return jsonify_response(success=True, message=message, data={'user_id': user.id}, status_code=201)
    else:
        # 注册失败
        logger.warning(f"[注册失败] 用户 {email} 注册失败: {message}")
        return jsonify_response(success=False, message=message, data=None, status_code=400)

@api.route('/auth/login', methods=['POST'])
def login():
    """用户登录接口"""
    data = request.json
    email = data.get('email')
    password = data.get('password')
    device_id = request.headers.get('X-Device-ID') # 从请求头获取设备ID
    totp_code = data.get('totp_code') # 获取TOTP代码

    if not email or not password:
        logger.warning(f"[登录] 请求缺少邮箱或密码: email={bool(email)}, password={bool(password)}")
        return jsonify_response(success=False, message='邮箱和密码不能为空', data=None, status_code=400)

    # 获取AuthService实例 (Now from g)
    auth_service_instance = g.auth_service
    
    user, token, message = auth_service_instance.login_user(email, password)

    if user:
        logger.info(f"[登录成功] 用户 {email} 登录成功")
        # 在这里创建 LoginSession 记录
        try:
            login_session_token = secrets.token_urlsafe(32) # 生成 login_token
            session_duration = timedelta(hours=2) # 例如，会话持续2小时
            expiration_timestamp = datetime.utcnow() + session_duration
            login_session = LoginSession(
                user_id=user.id,
                login_token=login_session_token, # 添加 login_token
                ip_address=get_real_ip(request),
                user_agent=request.user_agent.string,
                login_time=datetime.utcnow(),
                expiration_timestamp=expiration_timestamp # 添加 expiration_timestamp
            )
            db.session.add(login_session)
            db.session.commit()
            logger.info(f"用户 {user.email} (ID: {user.id}) 的登录会话已记录 (会话ID: {login_session.id})")
        except Exception as e:
            db.session.rollback()
            logger.error(f"记录用户 {user.email} 的登录会话时出错: {e}", exc_info=True)
            # 即使会话记录失败，登录本身也应成功

        return jsonify_response(success=True, 
                                message=message, 
                                data={
                                    'token': token, 
                                    'user': {
                                        'id': user.id, 
                                        'email': user.email, 
                                        'is_admin': user.is_admin
                                    }
                                },
                                status_code=200)
    else:
        logger.warning(f"[登录失败] 用户 {email} 登录失败: {message}")
        return jsonify_response(success=False, message=message, data=None, status_code=401)

@api.route('/auth/reset-password', methods=['POST'])
def reset_password():
    """重置密码API
    
    请求体:
    {
        "email": "邮箱",
        "code": "验证码",
        "new_password": "新密码"
    }
    
    返回:
    {
        "success": true/false,
        "message": "消息"
    }
    """
    data = request.json
    
    # 检查必填字段
    if not all(k in data for k in ['email', 'code', 'new_password']):
        logger.warning(f"[重置密码] 请求缺少必填字段: {data.keys()}")
        return jsonify_response(success=False, message='缺少必填字段', data=None, status_code=400)
    
    logger.info(f"[重置密码] 尝试重置密码: {data['email']}")
    
    # 重置密码
    success, message = auth_service.reset_password(
        email=data['email'],
        code=data['code'],
        new_password=data['new_password']
    )
    
    if success:
        logger.info(f"[重置密码] 重置密码成功: {data['email']}")
        return jsonify_response(success=True, message=message, data=None, status_code=200)
    else:
        logger.warning(f"[重置密码] 重置密码失败: {data['email']} - {message}")
        return jsonify_response(success=False, message=message, data=None, status_code=400)

# 用户信息路由
@api.route('/users/me', methods=['GET'])
@login_required
def get_user_info():
    """获取当前用户信息"""
    try:
        user = g.user
        logger.info(f"[用户信息] 用户 {user.id} 请求获取个人信息")
        
        # 获取用户订阅信息
        subscription = auth_service.get_user_subscription(user.id)
        
        # 获取用户设备信息
        devices = Device.query.filter_by(user_id=user.id).all()
        
        # 获取用户支付记录
        payments = PaymentRecord.query.filter_by(
            user_id=user.id
        ).order_by(
            PaymentRecord.created_at.desc()
        ).limit(10).all()
        
        logger.debug(f"[用户信息] 用户 {user.id} 获取到 {len(devices)} 个设备和 {len(payments)} 条支付记录")
        
        return jsonify_response(success=True, 
                                message="用户信息获取成功", # Added message for consistency
                                data={
                                    "user": user.to_dict(),
                                    "subscription": subscription.to_dict() if subscription else None,
                                    "devices": [device.to_dict() for device in devices],
                                    "payments": [payment.to_dict() for payment in payments]
                                },
                                status_code=200) # Added status_code for consistency
        
    except Exception as e:
        logger.exception(f"[用户信息] 获取用户 {g.user.id} 信息时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500)

@api.route('/users/me', methods=['PUT'])
@login_required
def update_user_info():
    """更新当前用户信息"""
    try:
        data = request.json
        if not data:
            logger.warning(f"[更新用户] 用户 {g.user.id} 提交了空的请求数据")
            return jsonify_response(success=False, message="无效的请求数据", data=None, status_code=400)
        
        user = g.user
        logger.info(f"[更新用户] 用户 {user.id} 正在更新个人信息: {list(data.keys())}")
        
        # 更新用户信息
        if 'email' in data:
            # 检查邮箱是否已被其他用户使用
            existing = User.query.filter(
                User.email == data['email'],
                User.id != user.id
            ).first()
            if existing:
                logger.warning(f"[更新用户] 用户 {user.id} 尝试更新邮箱为 {data['email']}，但该邮箱已被其他用户使用")
                return jsonify_response(success=False, message="邮箱已被使用", data=None, status_code=400)
            
            old_email = user.email
            user.email = data['email']
            logger.info(f"[更新用户] 用户 {user.id} 邮箱已从 {old_email} 更新为 {data['email']}")
        
        # 更新密码
        if 'password' in data:
            # 验证旧密码
            old_password = data.get('old_password')
            if not old_password or not user.check_password(old_password):
                logger.warning(f"[更新用户] 用户 {user.id} 尝试更新密码，但提供了错误的原密码")
                return jsonify_response(success=False, message="原密码错误", data=None, status_code=400)
            
            # 验证新密码强度
            is_valid, msg = auth_service.validate_password(data['password'])
            if not is_valid:
                logger.warning(f"[更新用户] 用户 {user.id} 提供的新密码强度不足: {msg}")
                return jsonify_response(success=False, message=msg, status_code=400)
            
            user.set_password(data['password'])
            logger.info(f"[更新用户] 用户 {user.id} 密码已更新")
        
        db.session.commit()
        logger.info(f"[更新用户] 用户 {user.id} 信息更新成功")
        
        return jsonify_response(success=True,
                                message="用户信息已更新",
                                data={"user": user.to_dict()})
        
    except Exception as e:
        logger.exception(f"[更新用户] 更新用户 {g.user.id} 信息时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", status_code=500)


@api.route('/devices', methods=['GET'])
@login_required
def get_devices():
    """获取用户的所有设备列表"""
    user_id = g.user.id
    logger.info(f"[设备] 用户 {user_id} 请求获取设备列表")
    
    devices = Device.query.filter_by(user_id=user_id).order_by(Device.created_at.desc()).all()
    
    devices_data = []
    for device in devices:
        devices_data.append({
            'id': device.id, # Use DB primary key for delete operations
            'name': device.device_name,
            'ip_address': device.device_ip,
            'device_type': device.device_type, # Changed key from 'type' to 'device_type'
            'created_at': device.created_at.isoformat() if device.created_at else None,
            'status': 'active'
        })
    
    logger.info(f"[设备] 用户 {user_id} 获取到 {len(devices_data)} 台设备信息")
            
    return jsonify_response(success=True, 
                            message="设备列表获取成功", 
                            data={'devices': devices_data}, # Ensure devices_data is wrapped in a 'devices' key within data
                            status_code=200)

@api.route('/devices', methods=['POST'])
@login_required
def register_device():
    """注册新设备"""
    try:
        data = request.json
        if not data:
            logger.warning(f"[设备注册] 用户 {g.user.id} 提交了空的请求数据")
            return jsonify_response(success=False, message="无效的请求数据", data=None, status_code=400)
        
        user = g.user
        
        # 获取设备信息
        device_id = data.get('device_id')
        device_info = {
            'name': data.get('name'),
            'type': data.get('type'),
            'platform': data.get('platform'),
            'ip_address': get_real_ip(request),
            'user_agent': request.headers.get('User-Agent', '')
        }
        
        logger.info(f"[设备注册] 用户 {user.id} 请求注册设备: ID={device_id}, 类型={data.get('type')}, IP={get_real_ip(request)}")
        
        if not device_id:
            logger.warning(f"[设备注册] 用户 {user.id} 未提供设备ID")
            return jsonify_response(success=False, message="设备ID不能为空", data=None, status_code=400)
        
        # 检查用户当前设备数
        device_count = Device.query.filter_by(user_id=user.id).count()
        
        # 获取用户的设备上限
        subscription = auth_service.get_user_subscription(user.id)
        max_devices = subscription.max_devices if subscription else 2
        
        logger.info(f"[设备注册] 用户 {user.id} 当前设备: {device_count}，上限: {max_devices}")
        
        # 用户设备数量限制
        if device_count >= max_devices:
            logger.warning(f"[设备注册] 用户 {user.id} 已达到设备数量上限 ({max_devices}个)")
            return jsonify_response(success=False, message=f"您已达到设备数量上限({max_devices}个)", data=None, status_code=400)
        
        # 注册设备
        success, device, message = device_auth_service.register_device(
            user.id, device_id, device_info
        )
        
        if not success:
            logger.warning(f"[设备注册] 用户 {user.id} 注册设备失败: {message}")
            return jsonify_response(success=False, message=message, data=None, status_code=400)
        
        # 获取设备详细信息
        success, device_info_retrieved, _ = device_auth_service.get_device_info(device_id) # Renamed device_info to avoid conflict
        
        logger.info(f"[设备注册] 用户 {user.id} 成功注册设备: ID={device_id}, DB_ID={device.id}")
        
        return jsonify_response(success=True,
                                message=message, # This message comes from device_auth_service
                                data=device_info_retrieved if success else device.to_dict(), # Use retrieved info if successful
                                status_code=200) # Added status_code for consistency
        
    except Exception as e:
        logger.exception(f"[设备注册] 用户 {g.user.id} 注册设备时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500)

@api.route('/devices/<device_id>', methods=['GET'])
@login_required
def get_device_info(device_id):
    """获取设备详细信息"""
    try:
        user = g.user
        logger.info(f"[设备] 用户 {user.id} 请求获取设备信息: {device_id}")
        
        # 验证设备属于当前用户
        device = Device.query.filter_by(user_id=user.id, device_id=device_id).first()
        if not device:
            return jsonify_response(success=False, message="设备不存在或不属于当前用户", data=None, status_code=404)
        
        # 获取设备详细信息
        success, device_info_retrieved, message_from_service = device_auth_service.get_device_info(device_id) # Renamed variables
        if not success:
            return jsonify_response(success=False, message=message_from_service, data=None, status_code=400) # message from service, data=None
        
        return jsonify_response(success=True,
                                message="设备详细信息获取成功", # Generic success message
                                data=device_info_retrieved, # Use retrieved info
                                status_code=200) # Added status_code
        
    except Exception as e:
        logger.exception(f"获取设备信息时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500)


@api.route('/devices/<device_id>', methods=['DELETE'])
@login_required
def delete_device(device_id):
    """用户删除自己的设备"""
    try:
        user = g.user
        
        # 验证设备属于当前用户
        device = Device.query.filter_by(id=device_id, user_id=user.id).first()
        if not device:
            return jsonify_response(success=False, message="设备不存在或不属于当前用户", data=None, status_code=404)
        
        # 获取请求信息用于审计
        ip_address = get_real_ip(request)
        user_agent = request.headers.get('User-Agent', '')
        
        # 使用设备服务删除设备（包含审计记录）
        from adspower_manager.services.device_service import DeviceService
        device_service = DeviceService()
        success = device_service.delete_device(
            device_id=device.id,
            ip_address=ip_address,
            user_agent=user_agent
        )
        
        if success:
            return jsonify_response(success=True,
                                    message="设备已删除",
                                    data=None, 
                                    status_code=200)
        else:
            return jsonify_response(success=False,
                                    message="删除设备失败",
                                    data=None,
                                    status_code=500)
        
    except Exception as e:
        logger.exception(f"删除设备时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500)

@api.route('/subscriptions', methods=['GET'])
@login_required
def get_user_subscription():
    """获取用户当前订阅信息"""
    try:
        user = g.user
        logger.info(f"[订阅] 用户 {user.id} 请求获取订阅信息")
        
        # 获取用户订阅信息
        subscription = auth_service.get_user_subscription(user.id)
        
        # 获取历史订阅记录
        history = Subscription.query.filter_by(
            user_id=user.id
        ).order_by(
            Subscription.created_at.desc()
        ).all()
        
        logger.info(f"[订阅] 用户 {user.id} 获取到当前订阅和 {len(history)} 条历史记录")
        
        return jsonify_response(success=True,
                                message="用户订阅信息获取成功",
                                data={
                                    "current": subscription.to_dict() if subscription else None,
                                    "history": [sub.to_dict() for sub in history]
                                },
                                status_code=200) # Added status_code for consistency
        
    except Exception as e:
        logger.exception(f"[订阅] 用户 {g.user.id} 获取订阅信息时出错: {str(e)}")
        return jsonify_response(success=False, message="服务器内部错误", data=None, status_code=500)

# 支付相关路由

@api.route('/subscriptions/calculate-renewal-date', methods=['POST'])
@login_required
def calculate_renewal_date():
    """计算续费后的新到期日"""
    data = request.json
    subscription_type_id = data.get('subscription_type_id')

    if not subscription_type_id:
        return jsonify_response(success=False, message="缺少订阅类型ID", status_code=400)

    try:
        user_id = g.user.id
        # 在 g 对象上获取或创建 subscription_service 实例
        if 'subscription_service' not in g:
            g.subscription_service = SubscriptionService()
        
        # 调用服务层方法计算新到期日
        new_expiry_date = g.subscription_service.calculate_renewal_expiry_date(user_id, subscription_type_id)

        if not new_expiry_date:
            return jsonify_response(success=False, message="无法计算新的到期日，可能订阅类型无效或用户无当前订阅。", status_code=400)

        return jsonify_response(
            success=True,
            message="新到期日计算成功",
            data={'new_expiry_date': new_expiry_date.strftime('%Y-%m-%d')},
            status_code=200
        )
    except Exception as e:
        logger.error(f"计算续费日期时出错: {e}", exc_info=True)
        return jsonify_response(success=False, message=f"计算续费日期时发生内部错误: {str(e)}", status_code=500)


@api.route('/payments/create', methods=['POST'])
@login_required
def create_payment():
    """创建易支付订单"""
    data = request.json
    subscription_type_id = data.get('subscription_type_id')
    payment_type = data.get('payment_type', 'alipay')  # 默认支付宝
    
    if not subscription_type_id:
        logger.warning(f"[创建支付] 用户 {g.user.id} 请求缺少 subscription_type_id")
        return jsonify_response(success=False, message="缺少订阅类型ID (subscription_type_id)", data=None, status_code=400)

    try:
        sub_type = SubscriptionType.query.get(subscription_type_id)
        if not sub_type:
            logger.warning(f"[创建支付] 用户 {g.user.id} 订阅类型ID {subscription_type_id} 无效")
            return jsonify_response(success=False, message=f"无效的订阅类型ID: {subscription_type_id}", data=None, status_code=404)
        
        # 检查用户是否已有活跃订阅
        existing_sub = Subscription.query.filter(
            Subscription.user_id == g.user.id,
            Subscription.end_date > datetime.utcnow()
        ).first()
        
        if existing_sub:
            # 如果已有不同类型的活跃订阅，阻止创建支付
            if existing_sub.subscription_type_id != int(subscription_type_id):
                current_plan_name = f"TypeID: {existing_sub.subscription_type_id}"
                try:
                    existing_plan_type = SubscriptionType.query.get(existing_sub.subscription_type_id)
                    if existing_plan_type:
                        current_plan_name = existing_plan_type.name
                except Exception as e_plan_name:
                    logger.warning(f"获取用户现有套餐名称时出错: {e_plan_name}")
                
                logger.warning(f"[创建支付] 用户 {g.user.id} 尝试购买 {sub_type.name} (ID: {subscription_type_id})，但已有活跃订阅 {current_plan_name} (ID: {existing_sub.subscription_type_id})")
                return jsonify_response(
                    success=False, 
                    message=f"您当前已有有效的订阅 '{current_plan_name}'，无法同时购买 '{sub_type.name}'。请等待当前订阅结束后再购买新的订阅，或联系客服处理。", 
                    data=None, 
                    status_code=400
                )
        
        amount = sub_type.price # 从订阅类型获取金额

        logger.info(f"[创建支付] 用户 {g.user.id} 请求为订阅类型 {sub_type.name} (ID: {subscription_type_id}) 创建支付，金额: {amount}，支付类型: {payment_type}")

        # 使用 EpayService
        epay_service_instance = getattr(g, 'epay_service', EpayService())
        
        payment_obj, redirect_html_or_url = epay_service_instance.create_payment_request(
            user_id=g.user.id,
            subscription_type_id=subscription_type_id,
            amount=amount,
            payment_type=payment_type,
            mode='submit'
        )
        
        if payment_obj:
            # 检查是否是调试模式的响应
            if isinstance(redirect_html_or_url, dict) and redirect_html_or_url.get('debug_mode'):
                # 测试环境的响应
                if redirect_html_or_url['success']:
                    payment_redirect_data = {
                        'type': 'debug_payment',
                        'success': True,
                        'message': redirect_html_or_url['message'],
                        'order_id': payment_obj.order_id,
                        'redirect_url': redirect_html_or_url['redirect_url']
                    }
                    logger.info(f"[创建支付成功-测试环境] 用户 {g.user.id} 的订单 {payment_obj.order_id} 已自动处理成功")
                else:
                    return jsonify_response(
                        success=False, 
                        message=f"测试环境支付处理失败: {redirect_html_or_url['message']}", 
                        data={'order_id': payment_obj.order_id}, 
                        status_code=500
                    )
            else:
                # 生产环境的正常响应
                payment_redirect_data = {
                    'type': 'html', 
                    'content': redirect_html_or_url, 
                    'order_id': payment_obj.order_id
                }
                logger.info(f"[创建支付成功] 用户 {g.user.id} 的订单 {payment_obj.order_id} 创建成功")
            
            return jsonify_response(success=True, message="支付订单创建成功", data=payment_redirect_data, status_code=200)
        else:
            logger.error(f"[创建支付] 用户 {g.user.id} 使用易支付创建支付失败")
            return jsonify_response(success=False, message="创建支付订单失败", data=None, status_code=500)

    except Exception as e:
        logger.exception(f"[创建支付] 用户 {g.user.id} 创建支付时发生严重错误: {e}")
        return jsonify_response(success=False, message=f"创建支付时发生内部错误: {str(e)}", data=None, status_code=500)

@api.route('/payments/epay/notify', methods=['GET', 'POST'])
def epay_notify():
    """易支付异步通知处理"""
    # 根据易支付的通知方式，数据可能在 form 或 args 中
    if request.method == 'POST':
        params = request.form.to_dict()
    else: # GET
        params = request.args.to_dict()
    
    logger.info(f"[易支付通知接收] 收到通知，方式: {request.method}, 参数: {params}")

    if not params:
        logger.warning("[易支付通知接收] 未收到任何参数。")
        return "fail", 200 # 返回 "fail" 给易支付，状态码 200

    # 获取 EpayService 实例
    epay_service_instance = getattr(g, 'epay_service', EpayService())

    # 1. 验证签名 (仅签名，不含业务逻辑)
    is_sign_valid = epay_service_instance._verify_notify_signature(params)
    if not is_sign_valid:
        logger.error(f"[易支付通知处理] 签名验证失败。参数: {params}")
        # 即使签名失败，也建议返回 "success" 给易支付，避免其不断重试无效请求。
        # 安全起见，此处可以返回 "fail"，但需监控易支付的重试行为。
        # 为减少复杂性，若签名直接失败，返回 "fail" (但状态码200)。
        return "fail", 200 

    # 2. 处理业务逻辑 (如果签名验证通过)
    # handle_notification 应该负责所有业务检查，包括订单是否存在、金额是否匹配、状态是否可处理等
    # 并最终调用 PaymentService 的核心处理逻辑
    processed_ok, response_to_epay = epay_service_instance.handle_notification(params)

    if processed_ok:
        logger.info(f"[易支付通知处理] 订单 {params.get('out_trade_no')} 业务处理完成，将返回 '{response_to_epay}' 给易支付。")
    else:
        logger.error(f"[易支付通知处理] 订单 {params.get('out_trade_no')} 业务处理遇到问题，将返回 '{response_to_epay}' 给易支付。")
        
    return response_to_epay, 200 # 根据 handle_notification 的结果返回 "success" 或 "fail"


@api.route('/payments/status/<order_id>', methods=['GET'])
@login_required
def query_payment_status(order_id):
    """查询支付状态 (供前端轮询)"""
    # 从 g 对象获取当前用户
    user = g.user
    
    logger.info(f"[支付] 用户 {g.user.id} 查询订单 {order_id} 状态") # 使用 g.user.id
    
    try:
        payment = Payment.query.filter_by(order_id=order_id, user_id=user.id).first()
    
        if payment is None:
            logger.error(f"[支付] 查询订单 {order_id} 失败或未找到")
            return jsonify_response(success=False, message='订单不存在或查询失败', status_code=404)

        # 确保支付状态是有效的字符串
        payment_status = payment.status if payment.status and isinstance(payment.status, str) else 'unknown'
        
        # 获取套餐名称
        plan_name = '未知套餐'
        if payment.subscription_type_id:
            sub_type = SubscriptionType.query.get(payment.subscription_type_id)
            if sub_type:
                plan_name = sub_type.name
            else:
                logger.warning(f"[支付] 订单 {order_id} 的 subscription_type_id '{payment.subscription_type_id}' 在 SubscriptionType 中未找到")

        # 构建基本响应数据
        payment_data = {
            'status': payment_status,  # 使用处理后的状态值
            'order_id': payment.order_id,
            'subscription_type_id': payment.subscription_type_id,
            'plan_name': plan_name, 
            'amount': payment.amount,
            'created_at': payment.created_at.isoformat() + 'Z',
            'paid_at': None,  # 初始化 paid_at
            'payment_url': payment.payment_url if hasattr(payment, 'payment_url') else None,  # 添加支付链接
            'error_message': payment.error_message if hasattr(payment, 'error_message') else None  # 添加错误信息
        }
        
        # 添加支付完成时间（如果有）
        if payment.paid_at:
            payment_data['paid_at'] = payment.paid_at.isoformat() + 'Z'

        # 查找用户的订阅信息（如果状态为paid）
        subscription = None
        if payment_status == 'paid':
            # 首先尝试通过payment_id直接查找订阅
            logger.info(f"[支付] 订单 {order_id} 状态为 {payment_status}，通过 payment_id 查找关联订阅")
            subscription = Subscription.query.filter_by(payment_id=payment.id).first()
            
            # 如果没找到，尝试通过其他方式查找最近创建的订阅
            if not subscription:
                logger.warning(f"[支付] 通过 payment_id={payment.id} 未找到订阅，尝试查找用户 {user.id} 最近的订阅")
                subscription = Subscription.query.filter_by(
                    user_id=user.id
                ).filter(
                    Subscription.subscription_type_id == payment.subscription_type_id
                ).order_by(
                    Subscription.created_at.desc()
                ).first()
                
                if subscription:
                    logger.info(f"[支付] 找到用户 {user.id} 的最近订阅 ID: {subscription.id}，但未关联到订单 {order_id}")
                    # 如果找到了订阅但未关联，可以考虑在这里更新关联
                    subscription.payment_id = payment.id
                    try:
                        db.session.commit()
                        logger.info(f"[支付] 已将订阅 {subscription.id} 关联到订单 {order_id}")
                    except Exception as e:
                        db.session.rollback()
                        logger.error(f"[支付] 更新订阅 {subscription.id} 关联订单 {order_id} 失败: {str(e)}", exc_info=True)
            
            if subscription:
                # 获取订阅类型名称
                subscription_type_name = '未知订阅类型'
                if subscription.subscription_type_id:
                    sub_type = SubscriptionType.query.get(subscription.subscription_type_id)
                    if sub_type:
                        subscription_type_name = sub_type.name
                
                # 获取订阅实例名称
                subscription_instance_name = '未知订阅实例'
                if subscription.subscription_instance_id:
                    sub_instance = SubscriptionInstance.query.get(subscription.subscription_instance_id)
                    if sub_instance:
                        subscription_instance_name = sub_instance.name
                
                payment_data['subscription'] = {
                    'id': subscription.id,
                    'start_date': subscription.start_date.isoformat() + 'Z',
                    'end_date': subscription.end_date.isoformat() + 'Z',
                    'subscription_type_id': subscription.subscription_type_id,
                    'subscription_type_name': subscription_type_name,
                    'subscription_instance_id': subscription.subscription_instance_id,
                    'subscription_instance_name': subscription_instance_name
                }
                logger.info(f"[支付] 订单 {order_id} 找到关联订阅 ID: {subscription.id}, 类型: {subscription_type_name}, 实例: {subscription_instance_name}")
            else:
                logger.warning(f"[支付] 订单 {order_id} (状态: {payment_status}) 未找到任何关联的订阅信息")

        # 返回统一格式的响应
        return jsonify_response(
            success=True,
            message='订单状态查询成功',
            data=payment_data
        )

    except Exception as e:
        logger.exception(f"[支付] 查询订单 {order_id} 状态时发生内部错误: {str(e)}")
        return jsonify_response(success=False, message='服务器内部错误', status_code=500)

@api.route('/payments/result', methods=['GET'])
def payment_result():
    """支付结果展示页面 (通用)
    处理来自易支付的回调并显示结果。
    """
    # 获取参数
    order_id = request.args.get('out_trade_no')
    
    if not order_id:
        return jsonify_response(success=False, message='缺少订单号参数', status_code=400)
    
    # 查询支付记录
    payment = Payment.query.filter_by(payment_id=order_id).first() # 使用 payment_id 查询
    
    if not payment:
        return jsonify_response(success=False, message='找不到支付订单', status_code=404)
    
    # 直接返回数据库状态
    logger.info(f"[支付结果页] 订单 {order_id} 查询状态: {payment.status}")
    
    # 获取关联的订阅信息
    subscription = None
    if payment and payment.subscription_id:
        subscription = Subscription.query.get(payment.subscription_id)
    
    # 组装响应
    response = {
        'success': payment.status == 'paid',
        'message': '支付成功' if payment.status == 'paid' else ('支付处理中' if payment.status == 'pending' else '支付状态未知'),
        'data': {
            'payment': {
                'order_id': payment.order_id,
                'amount': payment.amount,
                'status': payment.status,
                'created_at': payment.created_at.isoformat() if payment.created_at else None,
                'paid_at': payment.paid_at.isoformat() if payment.paid_at else None
            },
            'subscription': None
        }
    }
    
    if subscription:
        response['data']['subscription'] = {
            'id': subscription.id,
            'subscription_type_id': subscription.subscription_type_id, # ADDED: new ID field
            'plan_name': None, # Will be populated below
            'start_date': subscription.start_date.isoformat() if subscription.start_date else None,
            'end_date': subscription.end_date.isoformat() if subscription.end_date else None
        }
        # Populate plan_name for subscription
        if subscription.subscription_type_id:
            sub_type = SubscriptionType.query.get(subscription.subscription_type_id)
            if sub_type:
                response['data']['subscription']['plan_name'] = sub_type.name
            else:
                response['data']['subscription']['plan_name'] = f"未知类型 (ID: {subscription.subscription_type_id})"
    
    # 注意：此 API 端点现在只返回 JSON 数据。
    # 如果需要渲染 HTML 页面，应该在 app.py 或 page_routes.py 中定义页面路由。
    # 现有的 templates/payment_result.html 依赖此 API 返回 JSON。
    return jsonify_response(success=response['success'],
                            message=response['message'],
                            data=response['data'],
                            status_code=200)


@api.route('/adspower/direct-login', methods=['POST'])
@login_required
def request_direct_adspower_login():
    """处理直接登录AdsPower账号的请求 (已重构)
    
    现在调用 DirectLoginService 来处理核心逻辑。
    """
    # Ensure g.user is populated by @login_required
    if not hasattr(g, 'user') or not g.user:
        logger.error("[API Direct Login] Endpoint reached without authenticated user in g")
        return jsonify_response(success=False, message='认证失败，无法识别用户。', data=None, status_code=401)
        
    user_id = g.user.id
    logger.info(f"[API Direct Login] 收到用户 {user_id} 的直接登录请求")

    # --- Use the new DirectLoginService --- 
    try:
        # Import the getter function for the new service
        from ..services.direct_login_service import get_direct_login_service
        
        direct_login_service = get_direct_login_service()
        
        # Call the service method to prepare the login
        result = direct_login_service.prepare_login(user_id) # result is a dict
        
        # Determine status code based on result
        status_code = 500 # Default to internal error
        if result.get('success', False): # Safe access to 'success'
            status_code = 200
        elif result.get('error_code') in ['no_subscription', 'DEVICE_LIMIT_REACHED']:
             status_code = 403 # Forbidden
        elif result.get('error_code') in ['no_account_available', 'account_verification_failed']:
            status_code = 503 # Service Unavailable
        elif result.get('error_code') in ['user_not_found', 'session_creation_failed']:
             status_code = 400 # Bad Request or internal error
        
        logger.info(f"[API Direct Login] DirectLoginService processed request for user {user_id}. Success: {result.get('success', False)}. Status Code: {status_code}")
        # Use jsonify_response, passing components from the service's result
        # When success is False, include error_code in data
        response_data = result.get('data')
        if not result.get('success', False) and result.get('error_code'):
            if response_data is None:
                response_data = {}
            response_data['error_code'] = result.get('error_code')
        
        return jsonify_response(
            success=result.get('success', False),
            message=result.get('message', '处理直接登录请求时出错'), # Provide a default message
            data=response_data, # Pass data from service if available, with error_code added
            status_code=status_code
        )

    except Exception as e:
         # Catch unexpected errors during service call
         logger.exception(f"[API Direct Login] Unexpected error calling DirectLoginService for user {user_id}: {e}")
         return jsonify_response(
            success=False,
            message="处理登录请求时发生意外服务器错误",
            data={'error_code': 'service_call_exception', 'details': str(e)}, # Include error details in data
            status_code=500
         )

# 统一错误处理
@api.errorhandler(404)
def not_found(error):
    """处理404错误"""
    return jsonify_response(success=False, message="资源不存在", status_code=404)

@api.errorhandler(405)
def method_not_allowed(error):
    """处理405错误"""
    return jsonify_response(success=False, message="方法不允许", status_code=405)

@api.errorhandler(500)
def internal_server_error(error):
    """处理500错误"""
    return jsonify_response(success=False, message="服务器内部错误", status_code=500)

@api.route('/auth/generate-totp', methods=['POST'])
def generate_totp():
    """生成TOTP验证码
    
    请求体:
    {
        "login_token": "登录会话令牌" # 修改：不再是 secret
    }
    
    返回:
    {
        "success": true/false,
        "code": "生成的验证码",
        "remaining_seconds": 剩余有效秒数
    }
    """
    try:
        data = request.json
        if not data or 'login_token' not in data: # 修改：检查 login_token
            logger.warning("[TOTP Gen] 请求缺少 login_token 参数")
            return jsonify_response(success=False, message='缺少登录会话令牌', data=None, status_code=400)
            
        login_token = data['login_token']
        logger.info(f"[TOTP Gen]收到 login_token: {login_token[:10]}... 进行 TOTP 生成请求")

        login_session = LoginSession.query.filter_by(login_token=login_token).first()

        if not login_session:
            logger.warning(f"[TOTP Gen] 无效的 login_token: {login_token[:10]}...")
            return jsonify_response(success=False, message='登录会话令牌无效', data=None, status_code=404)
        
        now_utc_naive = datetime.now(timezone.utc).replace(tzinfo=None)
        if login_session.expiration_timestamp < now_utc_naive:
            logger.warning(f"[TOTP Gen] LoginSession {login_session.id} (token: {login_token[:10]}...) 已过期")
            return jsonify_response(success=False, message='登录会话已过期', data=None, status_code=410)

        adspower_account = AdspowerAccount.query.get(login_session.adspower_account_id)

        if not adspower_account:
            logger.error(f"[TOTP Gen] LoginSession {login_session.id} 关联的 AdspowerAccount ID {login_session.adspower_account_id} 未找到")
            return jsonify_response(success=False, message='无法找到关联的AdsPower账户', data=None, status_code=500)
        
        if not adspower_account.totp_secret:
            logger.warning(f"[TOTP Gen] AdspowerAccount ID {adspower_account.id} ({adspower_account.username}) 未配置TOTP密钥")
            return jsonify_response(success=False, message='关联的AdsPower账户未配置TOTP', data=None, status_code=400)

        secret = adspower_account.totp_secret
        
        import pyotp
        import time
        
        totp_instance = pyotp.TOTP(secret)
        code = totp_instance.now()
        
        current_time = int(time.time())
        time_step = 30
        time_remaining_in_window = time_step - (current_time % time_step)
        
        logger.info(f"[TOTP Gen] 为账户 {adspower_account.username} 生成代码 {code}, 剩余 {time_remaining_in_window}s")
        
        return jsonify_response(
            success=True,
            message='TOTP验证码生成成功',
            data={
                'code': code,
                'remaining_seconds': time_remaining_in_window
            },
            status_code=200
        )
        
    except Exception as e:
        logger.exception(f"[TOTP Gen] 生成TOTP验证码时出错: {str(e)}")
        return jsonify_response(success=False, message=f'生成验证码失败: {str(e)}', data=None, status_code=500)

@api.route('/subscriptions/current', methods=['GET'])
@login_required # 使用我们自己的 login_required 装饰器
def get_current_subscription():
    """获取当前用户的活跃订阅信息（已修改为获取最新订阅信息并由后端计算状态）"""
    user_id = g.user.id # 从 g 对象获取用户 ID
    
    try:
        # 获取用户最新的订阅记录，无论是否过期
        subscription = Subscription.query.filter_by(user_id=user_id)\
            .order_by(Subscription.end_date.desc())\
            .first()

        now_utc = datetime.utcnow() # Naive UTC datetime

        # --- 新增：初始化车次信息 ---
        subscription_instance_name = None
        subscription_instance_description = None
        # --- 车次信息初始化结束 ---

        if not subscription:
            # 用户没有任何订阅记录
            logger.info(f"[订阅查询] 用户 {user_id} 无任何订阅记录")
            subscription_data = {
                'id': None,
                'user_id': user_id,
                'plan': None,
                'plan_name': '无',
                'raw_start_date_utc_iso': None,
                'raw_end_date_utc_iso': None,
                'price': 0.0,
                'max_devices': 0,
                'is_expired': True,
                'status_text': '无订阅',
                'seconds_until_expiry': 0,
                'expiry_date_for_dashboard': '无订阅',
                'subscription_instance_name': subscription_instance_name, # <--- 添加车次名称
                'subscription_instance_description': subscription_instance_description # <--- 添加车次描述
            }
            return jsonify_response(success=True, message='当前无任何订阅记录', data={'subscription': subscription_data}, status_code=200)

        # 如果存在订阅记录
        # 假设 subscription.start_date 和 subscription.end_date 是 naive UTC from DB
        end_date_utc = subscription.end_date
        start_date_utc = subscription.start_date

        plan_display_name = f"TypeID: {subscription.subscription_type_id}"
        if subscription.subscription_type_details:
            plan_display_name = subscription.subscription_type_details.name

        logger.info(f"[订阅查询] 用户 {user_id} 最新订阅: ID={subscription.id}, Plan='{plan_display_name}', End={end_date_utc}")

        # --- 新增：查询关联的 SubscriptionInstance ---
        if subscription.subscription_instance_id:
            sub_instance = SubscriptionInstance.query.get(subscription.subscription_instance_id)
            if sub_instance:
                subscription_instance_name = sub_instance.name
                subscription_instance_description = sub_instance.description
                logger.info(f"[订阅查询] 用户 {user_id} 订阅关联的车次: ID={sub_instance.id}, Name='{sub_instance.name}'")
            else:
                logger.warning(f"[订阅查询] 用户 {user_id} 订阅关联的车次 ID {subscription.subscription_instance_id} 未找到!")
        # --- 车次查询结束 ---

        is_expired = now_utc >= end_date_utc
        status_text = "活跃" if not is_expired else "已到期"
        
        seconds_until_expiry = 0
        expiry_date_for_dashboard = ""

        if not is_expired:
            time_delta = end_date_utc - now_utc
            seconds_until_expiry = math.floor(max(0, time_delta.total_seconds()))
            expiry_date_for_dashboard = end_date_utc.strftime('%Y-%m-%d')
        else:
            # 如果已过期，仪表盘概览也显示状态文本
            expiry_date_for_dashboard = status_text # "已到期"

        plan_name_for_data = plan_display_name # Use the already determined plan_display_name

        subscription_data = {
            'id': subscription.id,
            'user_id': subscription.user_id,
            'subscription_type_id': subscription.subscription_type_id, # Keep the ID for reference
            'plan_name': plan_name_for_data, # Use the determined plan name
            'raw_start_date_utc_iso': start_date_utc.isoformat() + 'Z' if start_date_utc else None,
            'raw_end_date_utc_iso': end_date_utc.isoformat() + 'Z',
            'price': subscription.price,
            'max_devices': subscription.max_devices,
            'is_expired': is_expired,
            'status_text': status_text,
            'seconds_until_expiry': seconds_until_expiry,
            'expiry_date_for_dashboard': expiry_date_for_dashboard,
            'subscription_instance_name': subscription_instance_name, # <--- 添加车次名称
            'subscription_instance_description': subscription_instance_description # <--- 添加车次描述
        }
        
        return jsonify_response(success=True, message='当前订阅信息获取成功', data={'subscription': subscription_data}, status_code=200)
        
    except Exception as e:
        logger.exception(f"[订阅查询] 获取用户 {user_id} 订阅信息时出错: {e}")
        return jsonify_response(success=False, message=f'获取订阅信息时出错: {str(e)}', data=None, status_code=500)

@api.route('/adspower/login-info', methods=['GET'])
@login_required
def get_adspower_login_info():
    """
    获取当前用户订阅可用的ADSpower账号登录信息
    
    请求头:
    - Authorization: Bearer <token>
    
    返回:
    {
        "success": true/false,
        "message": "消息",
        "data": {
            "login_token": "登录令牌",
            "username": "ADSpower用户名",
            "password": "ADSpower密码",
            "totp_secret": "2FA密钥",
            "login_url": "登录URL"
        }
    }
    """
    try:
        user = g.user
        auth_service = g.auth_service
        
        # 调用auth_service获取登录信息
        login_info = auth_service.get_adspower_login_info(user.id)
        
        if not login_info or not login_info.get('success'): # Check for success flag from service
            # Extract message from service response if available
            message = "获取登录信息失败，可能是没有可用的ADSpower账号或您的订阅已过期"
            if login_info and 'message' in login_info:
                message = login_info['message']
            return jsonify_response(
                success=False, 
                message=message,
                data=None, # Ensure data is None on failure
                status_code=400
            )
        
        # If service call was successful, login_info should already contain success, message, data
        return jsonify_response(
            success=login_info.get('success', True), # Default to True if missing
            message=login_info.get('message', '登录信息获取成功'), # Default message
            data=login_info.get('data'), # Pass data from service
            status_code=200
        )
    
    except Exception as e:
        logger.exception(f"获取ADSpower登录信息时出错: {str(e)}")
        return jsonify_response(
            success=False, 
            message=f"服务器内部错误: {str(e)}",
            data=None, # Ensure data is None on exception
            status_code=500
        )

# 移除原有的账号池相关API，添加设置订阅类型的API
@api.route('/admin/accounts/adspower/<int:account_id>/subscription-type', methods=['POST'])
@admin_required
def set_adspower_account_subscription_type(account_id):
    """设置ADSpower账号的订阅类型
    
    请求体:
    {
        "subscription_type": "monthly" // 订阅类型代码
    }
    
    返回:
    {
        "success": true/false,
        "message": "设置结果信息"
    }
    """
    try:
        data = request.json
        if not data or 'subscription_type' not in data:
            return jsonify_response(
                success=False,
                message="缺少订阅类型参数",
                data=None,
                status_code=400
            )
        
        subscription_type_identifier = data['subscription_type'] # Can be id or code
        
        # 检查订阅类型是否存在 - 支持通过 id 或 code 查找
        if subscription_type_identifier: # Ensure it's not empty or None before querying
            subscription_type_obj = find_subscription_type(subscription_type_identifier)
            if not subscription_type_obj:
                return jsonify_response(
                    success=False,
                    message=f"无效的订阅类型: {subscription_type_identifier}",
                    data=None,
                    status_code=400
                )
        # Removed the 'else' block that would allow empty subscription_type, assuming it must be valid or None explicitly
        
        # 查找账号
        account = AdspowerAccount.query.get(account_id)
        if not account:
            return jsonify_response(
                success=False,
                message="未找到指定的AdsPower账号",
                data=None,
                status_code=404
            )
        
        # 注意：AdspowerAccount 模型已经不再有 subscription_type 字段
        # 现在通过 subscription_instance_id 关联到订阅实例
        # 这个接口可能需要重新设计来设置订阅实例而不是订阅类型
        logger.warning(f"[API] 此接口需要更新：AdspowerAccount 不再有 subscription_type 字段")
        db.session.commit()
        
        return jsonify_response(
            success=True,
            message="订阅类型设置成功",
            data=None, # No specific data to return on success
            status_code=200
        )
    except Exception as e:
        db.session.rollback()
        logger.error(f"设置AdsPower账号订阅类型失败: {e}", exc_info=True)
        return jsonify_response(
            success=False,
            message=f"设置失败: {str(e)}",
            data=None,
            status_code=500
        )

@api.route('/user/adspower-account', methods=['GET'])
@login_required
def get_user_adspower_account():
    """获取当前用户的ADSpower账号信息"""
    try:
        user = g.user
        # 获取用户最新的订阅记录
        subscription = Subscription.query.filter_by(user_id=user.id)\
            .order_by(Subscription.end_date.desc())\
            .first()

        if not subscription:
            return jsonify_response(
                success=False,
                message="当前用户没有任何订阅记录",
                data=None,
                status_code=404
            )

        # 获取与订阅关联的ADSpower账号
        adspower_account = AdspowerAccount.query.filter_by(subscription_id=subscription.id).first()

        if not adspower_account:
            return jsonify_response(
                success=False,
                message="当前订阅没有关联的ADSpower账号",
                data=None,
                status_code=404
            )

        # 获取与账号关联的设备数量
        device_count = Device.query.filter_by(adspower_account_id=adspower_account.id).count()

        # 构建账号信息
        account_info = {
            'id': adspower_account.id,
            'username': adspower_account.username,
            'password': adspower_account.password,
            'totp_secret': adspower_account.totp_secret,
            'is_active': adspower_account.is_active,
            'max_devices': adspower_account.max_devices,
            'current_devices': device_count,
            'subscription_id': subscription.id,
            'subscription_plan_name': subscription.subscription_type_details.name if subscription.subscription_type_details else f"TypeID: {subscription.subscription_type_id}",
            'subscription_end_date': subscription.end_date.isoformat() + 'Z'
        }

        return jsonify_response(
            success=True,
            message="ADSpower账号信息获取成功",
            data=account_info,
            status_code=200
        )
    except Exception as e:
        logger.exception(f"获取ADSpower账号信息时出错: {str(e)}")
        return jsonify_response(
            success=False,
            message=f"服务器内部错误: {str(e)}",
            data=None,
            status_code=500
        )

@api.route('/api/subscriptions/create', methods=['POST'])
@login_required
def create_subscription():
    """创建新的订阅"""
    try:
        data = request.json
        if not data or 'plan' not in data:
            return jsonify_response(
                success=False,
                message="缺少订阅计划参数",
                data=None,
                status_code=400
            )
        
        plan = data['plan']
        user_id = g.user.id
        
        # 检查订阅类型是否存在 - 支持通过 id 或 code 查找
        plan_type = find_subscription_type(plan)
        if not plan_type:
            return jsonify_response(
                success=False,
                message=f"无效的订阅计划: {plan}",
                data=None,
                status_code=400
            )
        
        # 检查用户是否已有活跃订阅
        active_subscription = Subscription.query.filter_by(user_id=user_id).filter(Subscription.end_date > datetime.utcnow()).first()
        if active_subscription:
            return jsonify_response(
                success=False,
                message="用户已有活跃订阅，无法创建新订阅",
                data=None,
                status_code=400
            )
        
        # 计算订阅开始和结束日期
        now_utc = datetime.utcnow()
        start_date_utc = now_utc
        end_date_utc = now_utc + timedelta(days=30)  # 默认30天
        
        # 创建新的订阅记录
        new_subscription = Subscription(
            user_id=user_id,
            plan=plan,
            start_date=start_date_utc,
            end_date=end_date_utc,
            price=plan_type.price,
            max_devices=plan_type.max_devices
        )
        db.session.add(new_subscription)
        db.session.commit()
        
        # 获取用户最新的订阅记录
        subscription = Subscription.query.filter_by(user_id=user_id)\
            .order_by(Subscription.end_date.desc())\
            .first()
        
        # 获取与订阅关联的ADSpower账号
        adspower_account = AdspowerAccount.query.filter_by(subscription_id=subscription.id).first()
        
        # 获取与账号关联的设备数量
        device_count = Device.query.filter_by(adspower_account_id=adspower_account.id).count()
        
        # 构建账号信息
        account_info = {
            'id': adspower_account.id,
            'username': adspower_account.username,
            'password': adspower_account.password,
            'totp_secret': adspower_account.totp_secret,
            'is_active': adspower_account.is_active,
            'max_devices': adspower_account.max_devices,
            'current_devices': device_count,
            'subscription_id': subscription.id,
            'subscription_plan_name': subscription.subscription_type_details.name if subscription.subscription_type_details else f"TypeID: {subscription.subscription_type_id}",
            'subscription_end_date': subscription.end_date.isoformat() + 'Z'
        }
        
        return jsonify_response(
            success=True,
            message="订阅创建成功",
            data=account_info,
            status_code=201
        )
    except Exception as e:
        db.session.rollback()
        logger.exception(f"创建订阅时出错: {str(e)}")
        return jsonify_response(
            success=False,
            message=f"服务器内部错误: {str(e)}",
            data=None,
            status_code=500
        )

"""
订阅类型管理API
"""
@api.route('/subscription-types', methods=['GET'])
@login_required
def get_subscription_types():
    """获取所有订阅类型
    
    返回:
    {
        "success": true,
        "types": [
            {
                "id": 1,
                "code": "monthly",
                "name": "月付会员",
                "max_devices": 5,
                "price": 49.99,
                "days": 30,
                "requirements": null,
                "is_public": true
            },
            ...
        ]
    }
    """
    try:
        user = g.user
        logger.info(f"[订阅类型] 用户 {user.id} 请求获取订阅类型列表")
        
        # 从数据库获取所有订阅类型
        subscription_types = SubscriptionType.query.all()
        
        # 转换为JSON格式
        types_data = []
        for type_obj in subscription_types:
            types_data.append({
                "id": type_obj.id,
                "code": type_obj.code,
                "name": type_obj.name,
                "max_devices": type_obj.max_devices,
                "price": type_obj.price,
                "days": type_obj.days,
                "requirements": type_obj.requirements,
                "is_public": type_obj.is_public,
                "default_subscription_instance_capacity": type_obj.default_subscription_instance_capacity
            })
        
        logger.info(f"[订阅类型] 获取到 {len(types_data)} 种订阅类型")
        
        return jsonify_response(success=True,
                                message="订阅类型列表获取成功",
                                data={"types": types_data})
    except Exception as e:
        logger.error(f"[订阅类型] 获取订阅类型失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"获取订阅类型失败: {str(e)}",
                                data=None, # Added data=None
                                status_code=500)

@api.route('/subscription-types', methods=['POST'])
@admin_required
def add_subscription_type():
    """添加新的订阅类型
    
    请求体:
    {
        "code": "monthly",
        "name": "月付会员",
        "max_devices": 5,
        "price": 49.99,
        "days": 30,
        "requirements": null,
        "is_public": true
    }
    
    返回:
    {
        "success": true,
        "message": "订阅类型添加成功",
        "data": {"type_id": 1}
    }
    """
    try:
        admin_user = g.user
        data = request.json
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 请求添加新订阅类型: {data}")
        
        # 验证必填字段 - code 现在是可选的
        if not data or 'name' not in data:
            logger.warning(f"[管理员] 添加订阅类型请求缺少必要参数: {data}")
            return jsonify_response(success=False,
                                    message="缺少必要参数 (name)",
                                    data=None, # Added data=None
                                    status_code=400)
        
        # 如果提供了 code，检查是否已存在
        if 'code' in data and data['code']:
            existing_type = SubscriptionType.query.filter_by(code=data['code']).first()
            if existing_type:
                logger.warning(f"[管理员] 添加订阅类型失败，代码 '{data['code']}' 已存在")
                return jsonify_response(success=False,
                                        message=f"订阅类型代码 '{data['code']}' 已存在",
                                        data=None, # Added data=None
                                        status_code=400)
        
        # 创建新的订阅类型
        new_type = SubscriptionType(
            code=data.get('code'),  # code 现在是可选的
            name=data['name'],
            max_devices=data.get('max_devices', 1),
            price=data.get('price', 0),
            days=data.get('days', 30),
            requirements=data.get('requirements'),
            is_public=data.get('is_public', True),
            default_subscription_instance_capacity=data.get('default_subscription_instance_capacity'),
            description=data.get('description')
        )
        
        db.session.add(new_type)
        db.session.commit()
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 成功添加订阅类型: {data.get('code', 'NO_CODE')} (ID: {new_type.id})")
        
        return jsonify_response(success=True,
                                message="订阅类型添加成功",
                                data={"type_id": new_type.id})
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 添加订阅类型失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"添加订阅类型失败: {str(e)}",
                                data=None, # Added data=None
                                status_code=500)

@api.route('/subscription-types/<int:type_id>', methods=['PUT'])
@admin_required
def update_subscription_type(type_id):
    """更新订阅类型
    
    请求体:
    {
        "code": "monthly",
        "name": "月付会员",
        "max_devices": 5,
        "price": 49.99,
        "days": 30,
        "requirements": null,
        "is_public": true
    }
    
    返回:
    {
        "success": true,
        "message": "订阅类型更新成功"
    }
    """
    try:
        admin_user = g.user
        data = request.json
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 请求更新订阅类型 ID: {type_id}")
        
        # 验证必填字段
        if not data:
            logger.warning(f"[管理员] 更新订阅类型请求体为空")
            return jsonify_response(success=False,
                                    message="请求体不能为空",
                                    data=None, # Added data=None
                                    status_code=400)
        
        # 查找订阅类型
        subscription_type = SubscriptionType.query.get(type_id)
        if not subscription_type:
            logger.warning(f"[管理员] 更新不存在的订阅类型 ID: {type_id}")
            return jsonify_response(success=False,
                                    message="未找到指定的订阅类型",
                                    data=None, # Added data=None
                                    status_code=404)
        
        # 如果要更改代码，检查新代码是否已存在
        if 'code' in data and data['code'] != subscription_type.code:
            existing_type = SubscriptionType.query.filter_by(code=data['code']).first()
            if existing_type:
                logger.warning(f"[管理员] 更新订阅类型失败，代码 '{data['code']}' 已存在")
                return jsonify_response(success=False,
                                        message=f"订阅类型代码 '{data['code']}' 已存在",
                                        data=None, # Added data=None
                                        status_code=400)
        
        # 更新字段
        changed_fields = []
        if 'code' in data:
            old_code = subscription_type.code
            subscription_type.code = data['code']
            changed_fields.append(f"code: {old_code} -> {data['code']}")
        if 'name' in data:
            old_name = subscription_type.name
            subscription_type.name = data['name']
            changed_fields.append(f"name: {old_name} -> {data['name']}")
        if 'max_devices' in data:
            old_max = subscription_type.max_devices
            subscription_type.max_devices = data['max_devices']
            changed_fields.append(f"max_devices: {old_max} -> {data['max_devices']}")
        if 'price' in data:
            old_price = subscription_type.price
            subscription_type.price = data['price']
            changed_fields.append(f"price: {old_price} -> {data['price']}")
        if 'days' in data:
            old_days = subscription_type.days
            subscription_type.days = data['days']
            changed_fields.append(f"days: {old_days} -> {data['days']}")
        if 'requirements' in data:
            old_req = subscription_type.requirements
            subscription_type.requirements = data['requirements']
            changed_fields.append(f"requirements: {old_req} -> {data['requirements']}")
        if 'is_public' in data:
            old_public = subscription_type.is_public
            subscription_type.is_public = data['is_public']
            changed_fields.append(f"is_public: {old_public} -> {data['is_public']}")
        if 'default_subscription_instance_capacity' in data:
            old_capacity = subscription_type.default_subscription_instance_capacity
            subscription_type.default_subscription_instance_capacity = data['default_subscription_instance_capacity']
            changed_fields.append(f"default_subscription_instance_capacity: {old_capacity} -> {data['default_subscription_instance_capacity']}")
        if 'description' in data:
            old_desc = subscription_type.description
            subscription_type.description = data['description']
            changed_fields.append(f"description: {old_desc} -> {data['description']}")
        
        db.session.commit()
        
        logger.info(f"[管理员] 管理员 {admin_user.id} 成功更新订阅类型 ID: {type_id}, 变更: {', '.join(changed_fields)}")
        
        return jsonify_response(success=True,
                                message="订阅类型更新成功",
                                data=None) # Added data=None for consistency, as no specific data is returned on success
    except Exception as e:
        db.session.rollback()
        logger.error(f"[管理员] 更新订阅类型 ID: {type_id} 失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"更新订阅类型失败: {str(e)}",
                                data=None, # Added data=None
                                status_code=500)

@api.route('/api/admin/accounts/adspower/batch-subscription-type', methods=['POST'])
@admin_required
def batch_update_adspower_account_subscription_type():
    """批量更新AdsPower账号的订阅类型
    
    请求体:
    {
        "account_ids": [1, 2, 3],
        "subscription_type": "monthly"
    }
    
    返回:
    {
        "success": true,
        "message": "成功更新X个账号的订阅类型",
        "updated_count": X
    }
    """
    try:
        data = request.json
        if not data:
            return jsonify_response(success=False,
                                    message="请求体不能为空",
                                    data=None, # Added data=None
                                    status_code=400)
        
        account_ids = data.get('account_ids')
        subscription_type = data.get('subscription_type')
        
        if not account_ids or not isinstance(account_ids, list) or len(account_ids) == 0:
            return jsonify_response(success=False,
                                    message="account_ids必须是非空数组",
                                    data=None, # Added data=None
                                    status_code=400)
        
        if not subscription_type:
            return jsonify_response(success=False,
                                    message="subscription_type不能为空",
                                    data=None, # Added data=None
                                    status_code=400)
        
        # 验证订阅类型是否存在
        subscription_type_obj = SubscriptionType.query.filter_by(code=subscription_type).first()
        if not subscription_type_obj:
            return jsonify_response(success=False,
                                    message=f"订阅类型 '{subscription_type}' 不存在",
                                    data=None, # Added data=None
                                    status_code=400)
        
        # 注意：AdspowerAccount 模型已经不再有 subscription_type 字段
        # 账号现在通过 subscription_instance_id 关联到订阅实例
        # 这个接口需要重新设计
        return jsonify_response(success=False,
                                message="此接口已废弃：AdspowerAccount 不再有 subscription_type 字段，请使用 subscription_instance_id",
                                data=None,
                                status_code=501)
    except Exception as e:
        db.session.rollback()
        logger.error(f"批量更新账号订阅类型失败: {e}", exc_info=True)
        return jsonify_response(success=False,
                                message=f"批量更新账号订阅类型失败: {str(e)}",
                                data=None, # Added data=None
                                status_code=500)

@api.route('/adspower/check-login-status', methods=['GET'])
def check_adspower_login_status():
    token = request.args.get('token')
    if not token:
        return jsonify_response(success=False, message='缺少令牌', data={'status': 'error'}, status_code=400)

    login_session = LoginSession.query.filter_by(login_token=token).first()

    if not login_session:
        return jsonify_response(success=False, message='令牌无效或已过期', data={'status': 'error'}, status_code=404)

    # 检查会话是否已过期 (基于 expiration_timestamp)
    now_utc_naive = datetime.now(timezone.utc).replace(tzinfo=None)
    if login_session.expiration_timestamp < now_utc_naive:
         # 不再更新数据库中的 status 字段
         logger.warning(f"LoginSession {login_session.id} has expired.")
         return jsonify_response(success=False, message='登录会话已过期', data={'status': 'expired'}, status_code=410) # HTTP 410 Gone

    # 检查会话是否已完成 (基于 completed_time)
    if login_session.completed_time:
         logger.info(f"LoginSession {login_session.id} already completed.")
         return jsonify_response(success=True, message='设备已确认，登录成功', data={'status': 'completed', 'loggedIn': True}, status_code=200)

    # --- 登录检查逻辑保持不变，但返回的状态需要推断 ---
    account = AdspowerAccount.query.get(login_session.adspower_account_id)
    if not account:
        logger.error(f"Associated AdspowerAccount {login_session.adspower_account_id} not found for session {login_session.id}.")
        return jsonify_response(success=False, message='关联的AdsPower账号不存在', data={'status': 'error'}, status_code=500)

    # 1. 加载初始快照
    known_devices_snapshot = []
    if login_session.known_devices_snapshot:
        try:
            known_devices_snapshot = json.loads(login_session.known_devices_snapshot)
            if not isinstance(known_devices_snapshot, list):
                logger.warning(f"LoginSession {login_session.id} known_devices_snapshot is not a list, treating as empty.")
                known_devices_snapshot = []
            else:
                logger.info(f"[DEBUG] Session {login_session.id}: 成功加载快照，包含 {len(known_devices_snapshot)} 个设备")
                # 打印快照内容用于调试
                for idx, device in enumerate(known_devices_snapshot):
                    logger.info(f"[DEBUG] 快照设备 {idx+1}: {json.dumps(device, ensure_ascii=False)}")
        except json.JSONDecodeError:
            logger.error(f"Failed to decode known_devices_snapshot for LoginSession {login_session.id}, treating as empty.", exc_info=True)
            known_devices_snapshot = []
    else:
        logger.info(f"[DEBUG] Session {login_session.id}: known_devices_snapshot 为空或None")
    logger.info(f"[DEBUG] Session {login_session.id}: 快照加载完成，共 {len(known_devices_snapshot)} 个设备")

    # 2. 获取当前设备列表
    adspower_api = get_adspower_api()
    current_devices = None
    try:
        logger.info(f"[DEBUG] Session {login_session.id}: 正在获取账号 {account.id} 的当前设备...")
        current_devices = adspower_api.get_devices_info(account)
    except Exception as e:
         logger.error(f"Error getting current devices for account {account.id} during check-login-status (session {login_session.id}): {e}", exc_info=True)
         return jsonify_response(success=False, message=f'获取当前设备列表时出错，请稍后重试', data={'status': 'pending'}, status_code=200) # Success false, but 200 for pending status

    if current_devices is None:
        logger.warning(f"get_devices_info returned None for account {account.id} during check-login-status (session {login_session.id}).")
        # 返回 pending 让用户重试
        # 推断状态为 pending
        return jsonify_response(success=False, message='无法获取当前设备列表，请稍后重试', data={'status': 'pending'}, status_code=200) # Success false, but 200 for pending status
    
    logger.info(f"[DEBUG] Session {login_session.id}: 从API获取到 {len(current_devices)} 个当前设备")
    # 打印当前设备用于调试
    for idx, device in enumerate(current_devices):
        logger.info(f"[DEBUG] 当前设备 {idx+1}: {json.dumps(device, ensure_ascii=False)}")
    
    logger.debug(f"Session {login_session.id}: Got {len(current_devices)} current devices from API.")

    # 3. 比较列表，查找新设备
    # 优先使用 device_id 进行比较，如果没有则回退到 name+type
    known_device_ids = set()
    known_device_tuples = set()
    
    for d in known_devices_snapshot:
        if isinstance(d, dict):
            logger.info(f"[DEBUG] 快照设备: {json.dumps(d, ensure_ascii=False)}")
            
            # 优先获取 device_id
            device_id = d.get('id') or d.get('device_id') or (d.get('raw_data', {}).get('device_id') if isinstance(d.get('raw_data'), dict) else None)
            if device_id:
                known_device_ids.add(device_id)
                logger.info(f"[DEBUG] 添加已知设备ID: '{device_id}'")
            
            # 同时记录 name+type 作为后备
            name = d.get('name') or d.get('device_name')
            dtype = d.get('device_type') 
            if name and dtype: 
                known_device_tuples.add((name, dtype))
                logger.info(f"[DEBUG] 添加已知设备(name+type): name='{name}', type='{dtype}'")
            else:
                logger.warning(f"[DEBUG] 快照设备缺少 name 或 type: {json.dumps(d, ensure_ascii=False)}")
    
    logger.info(f"[DEBUG] Session {login_session.id}: 已知设备ID集合: {known_device_ids}, 已知设备元组集合: {known_device_tuples}")

    newly_detected_devices = []
    for device in current_devices:
        if isinstance(device, dict):
            # 优先检查 device_id
            current_device_id = device.get('id') or device.get('device_id') or (device.get('raw_data', {}).get('device_id') if isinstance(device.get('raw_data'), dict) else None)
            
            if current_device_id:
                # 如果有 device_id，优先使用它进行比较
                if current_device_id not in known_device_ids:
                    logger.info(f"[DEBUG] ✅ 通过device_id发现新设备: ID={current_device_id}, Name={device.get('name')}, Type={device.get('device_type')}")
                    newly_detected_devices.append(device)
                else:
                    logger.info(f"[DEBUG] 设备ID '{current_device_id}' 已在快照中存在")
            else:
                # 如果没有 device_id，回退到 name+type 比较
                current_name = device.get('name') or device.get('device_name')
                current_type = device.get('device_type')
                
                if current_name and current_type:
                    device_tuple = (current_name, current_type)
                    logger.info(f"[DEBUG] 使用name+type检查设备: name='{current_name}', type='{current_type}'")
                    if device_tuple not in known_device_tuples:
                        logger.info(f"[DEBUG] ✅ 通过name+type发现新设备: Name={current_name}, Type={current_type}")
                        newly_detected_devices.append(device)
                    else:
                        logger.info(f"[DEBUG] 该设备(name+type)已在快照中存在")
                else:
                    logger.warning(f"[DEBUG] 当前设备缺少必要信息进行比较: {json.dumps(device, ensure_ascii=False)}")
        else:
            logger.warning(f"[DEBUG] 无效的设备格式: {device}")

    # 4. 根据比较结果返回 (推断状态)
    if newly_detected_devices:
        logger.info(f"New device detected for session {login_session.id}: {newly_detected_devices[0]}")
        return jsonify_response(success=True,
                                message='检测到新设备登录，请确认',
                                data={
                                    'status': 'new_device_detected',
                                    'new_device': newly_detected_devices[0]
                                }, status_code=200)
    else:
        logger.info(f"No new device detected for session {login_session.id}. Status remains pending.")
        return jsonify_response(success=False, # 表示登录流程尚未完成
                                message='尚未检测到新设备登录',
                                data={
                                    'status': 'pending'
                                }, status_code=200) # Success false, but 200 for pending status

# 添加明确的API路由别名，确保前端能够正确访问
@api.route('/adspower/check-login-status', methods=['GET'])
def check_login_status_api_alias():
    """检查登录状态API路由别名，确保前端能够正确访问"""
    return check_adspower_login_status()

# ===== 健康检查和调试API =====

@api.route('/devices/confirm-new', methods=['POST'])
def confirm_new_device():
    """用户确认新设备登录"""
    data = request.json
    if not data or 'login_token' not in data or 'device' not in data:
        return jsonify_response(success=False, message='缺少令牌或设备信息', data=None, status_code=400)

    token = data.get('login_token')
    new_device_info = data.get('device')

    if not isinstance(new_device_info, dict):
         return jsonify_response(success=False, message='设备信息格式无效', data=None, status_code=400)

    login_session = LoginSession.query.filter_by(login_token=token).first()

    if not login_session:
        return jsonify_response(success=False, message='登录会话无效或已过期', data=None, status_code=404)

    # 再次检查会话是否已过期
    now = datetime.now(timezone.utc).replace(tzinfo=None)
    if login_session.expiration_timestamp < now: 
        logger.warning(f"Attempt to confirm device on expired session {login_session.id}")
        return jsonify_response(success=False, message='登录会话已过期', data=None, status_code=410)

    # 检查会话状态 (基于 completed_time)，防止重复确认
    if login_session.completed_time:
         logger.warning(f"Attempt to re-confirm device for completed session {login_session.id}")
         return jsonify_response(success=True, message='设备已确认，登录已完成', data=None, status_code=200) # 返回成功，因为已完成

    # 获取用户信息
    user = User.query.get(login_session.user_id)
    if not user:
         logger.error(f"User {login_session.user_id} not found for session {login_session.id}")
         return jsonify_response(success=False, message='无法找到关联用户', data=None, status_code=500)

    # 检查用户设备数量是否达到上限
    active_subscription = g.auth_service.get_user_subscription(user.id) 
    if not active_subscription: 
         logger.warning(f"User {user.id} has no active subscription during device confirmation.")
         return jsonify_response(success=False, message='您没有有效的订阅来添加设备', data=None, status_code=403)

    current_device_count = Device.query.filter_by(user_id=user.id).count()
    max_devices_allowed = active_subscription.max_devices 
    if current_device_count >= max_devices_allowed:
        logger.warning(f"User {user.id} reached device limit ({current_device_count}/{max_devices_allowed}) during confirmation.")
        return jsonify_response(
            success=False,
            message=f'您的设备数量已达上限 ({max_devices_allowed}台)，无法添加新设备。请先在设备管理中登出不再使用的设备。',
            data=None, # Added data=None
            status_code=403)

    # --- 处理新设备信息 ---
    # 获取设备ID（优先从多个可能的字段获取）
    device_id = (new_device_info.get('id') or 
                 new_device_info.get('device_id') or 
                 (new_device_info.get('raw_data', {}).get('device_id') if isinstance(new_device_info.get('raw_data'), dict) else None))
    
    device_name = new_device_info.get('name') or new_device_info.get('device_name')
    device_type = new_device_info.get('device_type')
    device_ip = new_device_info.get('ip_address') or new_device_info.get('device_ip')
    last_login_str = new_device_info.get('last_open') or new_device_info.get('login_time')

    if not device_name or not device_type:
         logger.error(f"New device info missing name or type in session {login_session.id}: {new_device_info}")
         return jsonify_response(success=False, message='提供的设备信息不完整 (缺少名称或类型)', data=None, status_code=400)

    try:
        # 优先通过 device_id 查找设备，如果没有则通过 name+type 查找
        existing_device = None
        
        if device_id:
            # 先检查是否有任何用户使用了这个 device_id
            device_with_same_id = Device.query.filter_by(device_id=device_id).first()
            if device_with_same_id and device_with_same_id.user_id != user.id:
                logger.error(f"设备ID '{device_id}' 已被其他用户(ID: {device_with_same_id.user_id})使用")
                return jsonify_response(success=False, message='设备标识符已被占用，请联系管理员', data=None, status_code=409)
            
            # 优先通过 device_id 查找当前用户的设备
            existing_device = Device.query.filter_by(
                user_id=user.id,
                device_id=device_id
            ).first()
            
            if existing_device:
                logger.info(f"Found existing device by device_id '{device_id}' for user {user.id}")
        
        if not existing_device:
            # 如果没找到，尝试通过 name+type 查找
            existing_device = Device.query.filter_by(
                user_id=user.id,
                device_name=device_name,
                device_type=device_type
            ).first()
            
            if existing_device:
                logger.info(f"Found existing device by name+type for user {user.id}")

        if existing_device:
            logger.info(f"Updating existing device for user {user.id}")
            # 更新设备信息
            if device_id and not existing_device.device_id:
                # 如果旧设备没有 device_id，现在补充上
                existing_device.device_id = device_id
                logger.info(f"Added device_id '{device_id}' to existing device record")
            existing_device.device_ip = device_ip
            existing_device.adspower_account_id = login_session.adspower_account_id
            db.session.add(existing_device)
        else:
            logger.info(f"Creating new device record for user {user.id} with device_id: {device_id}")
            new_db_device = Device(
                user_id=user.id,
                adspower_account_id=login_session.adspower_account_id,
                device_id=device_id,  # 存储设备ID
                device_name=device_name,
                device_type=device_type,
                device_ip=device_ip
            )
            db.session.add(new_db_device)

        # 更新会话状态为完成 (设置 completed_time)
        # 移除: login_session.status = 'completed'
        login_session.completed_time = datetime.utcnow()
        db.session.add(login_session)

        # 提交数据库更改
        db.session.commit()
        
        # 记录设备审计日志
        if not existing_device:
            # 只有新设备才记录注册日志
            from adspower_manager.services.device_audit_service import DeviceAuditService
            
            # 获取 AdsPower 账号信息
            adspower_account = AdspowerAccount.query.get(login_session.adspower_account_id) if login_session.adspower_account_id else None
            
            device_snapshot = {
                'device_id': device_id,
                'device_name': device_name,
                'device_type': device_type,
                'device_ip': device_ip,
                'adspower_account_id': login_session.adspower_account_id,
                'adspower_username': adspower_account.username if adspower_account else None,
                'subscription_instance_id': adspower_account.subscription_instance_id if adspower_account else None
            }
            
            DeviceAuditService.log_device_action(
                device_id=new_db_device.id,
                user_id=user.id,
                action=DeviceAuditService.ACTION_REGISTER,
                action_source='user',  # 用户自己通过 ADSPower 登录确认设备
                description=f"用户 {user.email} 通过ADSPower登录确认新设备 {device_name}",
                device_snapshot=device_snapshot,
                new_value={'status': 'active', 'adspower_account_id': login_session.adspower_account_id},
                ip_address=get_real_ip(request),
                user_agent=request.headers.get('User-Agent', '')
            )

        logger.info(f"Device confirmed and session {login_session.id} completed for user {user.id}.")
        return jsonify_response(success=True, message='新设备已成功确认并添加', data=None, status_code=200)

    except Exception as e:
        db.session.rollback()
        logger.error(f"Error confirming device or updating session {login_session.id}: {e}", exc_info=True)
        return jsonify_response(success=False, message='处理设备确认时发生内部错误', data=None, status_code=500)

@api.route('/adspower/cancel-login', methods=['POST'])
def cancel_adspower_login():
    """用户点击"不是我的设备"或主动取消登录"""
    data = request.json
    token = data.get('login_token')
    
    if not token:
        return jsonify_response(success=False, message='缺少登录令牌', data=None, status_code=400)
    
    login_session = LoginSession.query.filter_by(login_token=token).first()
    if not login_session:
        return jsonify_response(success=False, message='无效的登录会话', data=None, status_code=404)
    
    # 检查是否已完成
    if login_session.completed_time:
        return jsonify_response(success=False, message='登录会话已完成', data=None, status_code=400)
    
    # 检查会话是否已过期
    now_utc = datetime.now(timezone.utc).replace(tzinfo=None)
    if login_session.expiration_timestamp < now_utc:
        return jsonify_response(success=False, message='登录会话已过期', data=None, status_code=410)
    
    try:
        # 标记为取消并使令牌过期
        login_session.login_status = 'cancelled'
        login_session.logout_time = datetime.utcnow()
        # 将过期时间设置为当前时间，使令牌立即过期
        login_session.expiration_timestamp = datetime.utcnow()
        login_session.completed_time = datetime.utcnow()  # 标记为已完成，防止重复使用
        db.session.commit()
        
        logger.info(f"用户主动取消登录会话 {login_session.id} (用户: {login_session.user_id})，令牌已设置为过期")
        return jsonify_response(success=True, message='登录已取消', data=None, status_code=200)
    except Exception as e:
        db.session.rollback()
        logger.error(f"取消登录会话 {login_session.id} 时出错: {e}", exc_info=True)
        return jsonify_response(success=False, message='取消登录失败', data=None, status_code=500)

# === 设备登出 API ===
@api.route('/devices/<int:device_db_id>/logout', methods=['POST'])
@login_required
def logout_device_api(device_db_id):
    """处理用户请求退出指定设备的登录

    Args:
        device_db_id (int): 设备在数据库中的主键 ID

    Returns:
        JSON: 操作结果
    """
    user = g.user
    logger.info(f"用户 {user.id} 请求退出设备 DB ID: {device_db_id}")

    # 1. 查找设备并验证归属
    device = Device.query.filter_by(id=device_db_id, user_id=user.id).first()
    if not device:
        logger.warning(f"用户 {user.id} 尝试退出不属于自己或不存在的设备 DB ID: {device_db_id}")
        return jsonify_response(success=False, message="设备不存在或您无权操作此设备", data=None, status_code=404)

    # 2. 检查设备是否已关联 AdsPower 账号
    if not device.adspower_account_id:
        # 如果没有关联账号，理论上无法在 AdsPower 退出，直接删除本地记录
        logger.warning(f"设备 DB ID: {device_db_id} 未关联 AdsPower 账号，将直接删除本地记录") # Updated log
        try:
            # 记录审计日志
            from adspower_manager.services.device_audit_service import DeviceAuditService
            ip_address = get_real_ip(request)
            user_agent = request.headers.get('User-Agent', '')
            
            device_snapshot = {
                'device_id': device.device_id,
                'device_name': device.device_name,
                'device_type': device.device_type,
                'device_ip': device.device_ip,
                'adspower_account_id': None
            }
            
            DeviceAuditService.log_device_action(
                device_id=device.id,
                user_id=device.user_id,
                action=DeviceAuditService.ACTION_DELETE,
                action_source='user',
                description=f"用户删除未关联账号的设备 {device.device_name or device.device_id}",
                device_snapshot=device_snapshot,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.session.delete(device) # Delete the record
            db.session.commit()
            return jsonify_response(success=True, message="设备未关联远程账号，本地记录已删除", data=None, status_code=200) # Updated message
        except Exception as e:
            db.session.rollback()
            logger.error(f"删除未关联账号的设备 {device_db_id} 记录时数据库出错: {e}", exc_info=True) # Updated log
            return jsonify_response(success=False, message="删除本地设备记录时出错", data=None, status_code=500)

    # 3. 获取关联的 AdsPower 账号
    adspower_account = AdspowerAccount.query.get(device.adspower_account_id)
    if not adspower_account:
        logger.error(f"设备 DB ID: {device_db_id} 关联的 AdsPower 账号 ID: {device.adspower_account_id} 不存在，将删除本地设备记录") # Updated log
        try:
            # 记录审计日志
            from adspower_manager.services.device_audit_service import DeviceAuditService
            ip_address = get_real_ip(request)
            user_agent = request.headers.get('User-Agent', '')
            
            device_snapshot = {
                'device_id': device.device_id,
                'device_name': device.device_name,
                'device_type': device.device_type,
                'device_ip': device.device_ip,
                'adspower_account_id': device.adspower_account_id
            }
            
            DeviceAuditService.log_device_action(
                device_id=device.id,
                user_id=device.user_id,
                action=DeviceAuditService.ACTION_DELETE,
                action_source='user',
                description=f"用户删除关联账号不存在的设备 {device.device_name or device.device_id}",
                device_snapshot=device_snapshot,
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            db.session.delete(device) # Delete the record
            db.session.commit()
            return jsonify_response(success=True, message="关联的AdsPower账号不存在，本地设备记录已删除", data=None, status_code=200) # Updated message
        except Exception as e:
            db.session.rollback()
            logger.error(f"删除关联账号不存在的设备 {device_db_id} 记录时数据库出错: {e}", exc_info=True) # Updated log
            return jsonify_response(success=False, message="删除本地设备记录时出错", data=None, status_code=500)

    # 4. 获取设备在 AdsPower 上的名称 (必须有 device_name)
    device_name_on_ads = device.device_name
    if not device_name_on_ads:
        logger.error(f"设备 DB ID: {device_db_id} 缺少在 AdsPower 上的设备名称 (device_name)，无法执行远程退出")
        return jsonify_response(success=False, message="设备缺少必要信息，无法执行远程退出操作", data=None, status_code=400)

    # 4.1 获取设备的 IP 地址 (device_ip)
    device_ip_on_ads = device.device_ip
    if not device_ip_on_ads:
        # 如果 IP 地址不存在，记录警告但仍尝试仅用名称退出 (或根据需要决定是否强制要求 IP)
        logger.warning(f"设备 DB ID: {device_db_id} 缺少 IP 地址 (device_ip)，将尝试仅使用名称退出")

    # 4.2 获取设备的类型 (device_type)
    device_type_on_ads = device.device_type
    if not device_type_on_ads:
        logger.error(f"设备 DB ID: {device_db_id} 缺少设备类型 (device_type)，无法执行精确的远程退出操作")
        return jsonify_response(success=False, message="设备缺少类型信息，无法执行远程退出操作", data=None, status_code=400)

    # 5. 调用 AdsPower API 执行退出操作
    try:
        adspower_api = get_adspower_api()
        # 移除 device_ip_on_ads 参数
        logout_success, message = adspower_api.logout_device(adspower_account, device_name_on_ads, device_type_on_ads)
        logger.info(f"adspower_api.logout_device returned: success={logout_success}, message='{message}'")

        if logout_success is True:
            logger.info(f"AdsPower操作成功或无需操作 (原因: '{message}')，准备删除本地设备 DB ID: {device_db_id}")
            try:
                # 记录设备登出审计日志
                from adspower_manager.services.device_audit_service import DeviceAuditService
                ip_address = get_real_ip(request)
                user_agent = request.headers.get('User-Agent', '')
                
                device_snapshot = {
                    'device_id': device.device_id,
                    'device_name': device.device_name,
                    'device_type': device.device_type,
                    'device_ip': device.device_ip,
                    'adspower_account_id': device.adspower_account_id
                }
                
                DeviceAuditService.log_device_action(
                    device_id=device.id,
                    user_id=device.user_id,
                    action=DeviceAuditService.ACTION_DELETE,
                    action_source='user',  # 用户自己删除
                    description=f"用户主动删除设备 {device.device_name or device.device_id}",
                    device_snapshot=device_snapshot,
                    ip_address=ip_address,
                    user_agent=user_agent
                )
                
                db.session.delete(device) # Delete the record
                db.session.commit()
                logger.info(f"设备 DB ID: {device_db_id} 本地设备记录已删除")
                final_message = message
                return jsonify_response(success=True, message=final_message, data=None, status_code=200)
            except Exception as e:
                db.session.rollback()
                logger.error(f"AdsPower操作成功/无需操作后，删除设备 {device_db_id} 本地记录时数据库出错: {e}", exc_info=True)
                return jsonify_response(success=False, message=f"远程操作成功/无需操作，但删除本地记录失败: {e}", data=None, status_code=500)
        else:
            logger.error(f"AdsPower操作失败 (DB ID: {device_db_id})，原因: {message}")
            return jsonify_response(success=False, message=message or "远程退出设备失败", data=None, status_code=500)

    except Exception as e:
        logger.exception(f"调用 logout_device 时发生意外错误 (设备 DB ID: {device_db_id}): {e}")
        return jsonify_response(success=False, message=f"执行退出操作时发生服务器内部错误: {str(e)}", data=None, status_code=500)

# ===== 健康检查和调试API =====

def get_user_subscription_status(user_id):
    """辅助函数：获取用户订阅状态（只读）"""
    subscription = Subscription.query.filter(
        Subscription.user_id == user_id,
        Subscription.end_date > datetime.utcnow()
    ).order_by(Subscription.end_date.desc()).first()
    
    # subscription_status = subscription.status if subscription else '无订阅' # 移除 status
    is_active = bool(subscription) # 直接判断是否存在有效订阅
    plan = subscription.plan if subscription else None
    end_date = subscription.end_date.isoformat() + 'Z' if subscription else None
    # 移除 status 返回值
    # return is_active, plan, end_date, subscription_status
    return is_active, plan, end_date

# Keycloak OIDC 认证路由
@api.route('/auth/keycloak/login')
def keycloak_login():
    """重定向到Keycloak进行登录"""
    # 确保OAuth客户端已初始化
    if 'keycloak' not in oauth_clients._clients:
         init_oauth(current_app) # 传递当前的 current_app 实例, was app

    # Flask 的 redirect_uri 必须使用 url_for 生成，并确保 _external=True
    # 这个 redirect_uri 必须与 Keycloak 中配置的完全一致。
    # 我们在 config.py 中定义了 OIDC_REDIRECT_URI，应该以那个为准。
    # redirect_uri = url_for('api.keycloak_authorize_callback', _external=True)
    # 使用配置中的 redirect_uri，因为它更可靠
    configured_redirect_uri = current_app.config['OIDC_REDIRECT_URI'] # was app.config
    
    # 生成并存储 state 参数以防止CSRF
    # state = secrets.token_urlsafe(16)
    # session['oidc_state'] = state # 需要 app.secret_key 和 session 支持
    # return oauth_clients.keycloak.authorize_redirect(configured_redirect_uri, state=state)
    return oauth_clients.keycloak.authorize_redirect(configured_redirect_uri)

@api.route('/oidc/callback') # 这个路径应该与 OIDC_REDIRECT_URI 的路径部分匹配
def keycloak_authorize_callback():
    """处理Keycloak的回调"""
    if 'keycloak' not in oauth_clients._clients:
        init_oauth(current_app) # was app

    if 'auth_service' not in g:
        # logger.debug("[OIDC Callback] AuthService not in g, creating new instance for current_app.")
        g.auth_service = AuthService(current_app) # was app
        # g.auth_service.init_app(current_app) # was app

    try:
        # 验证 state 参数 (如果使用的话)
        # received_state = request.args.get('state')
        # expected_state = session.pop('oidc_state', None)
        # if not received_state or received_state != expected_state:
        #     logger.warning("[OIDC Callback] Invalid state parameter.")
        #     return jsonify({'success': False, 'message': 'Invalid state parameter.'}), 400

        token_response = oauth_clients.keycloak.authorize_access_token()
        userinfo = token_response.get('userinfo')
        if not userinfo:
            # 有些 OIDC provider 可能在 id_token 中包含所有信息
            # 或者需要显式调用 userinfo endpoint
            # 对于Keycloak, userinfo() 通常可用，或者解析 id_token
            userinfo = oauth_clients.keycloak.userinfo() # 尝试再次获取
            if not userinfo:
                 id_token_claims = oauth_clients.keycloak.parse_id_token(token_response)
                 userinfo = id_token_claims

        if not userinfo or not userinfo.get('email'):
            logger.error("[OIDC Callback] 未能从Keycloak获取用户信息或邮箱为空")
            return jsonify_response(success=False, message='未能从Keycloak获取用户信息或邮箱为空', data=None, status_code=400)

        email = userinfo.get('email')
        logger.info(f"[OIDC Callback] 用户信息已获取: {email}")

        # 查找或创建本地用户
        user = User.query.filter_by(email=email).first()
        if not user:
            if current_app.config['OIDC_JIT_USER_PROVISIONING']: # was app.config
                logger.info(f"[OIDC Callback] 用户 {email} 不存在，将进行即时创建 (JIT Provisioning)")
                # 创建新用户，密码可以设为随机不可用值，因为他们通过Keycloak登录
                # is_admin 角色可以考虑从Keycloak的token中获取 (如果配置了角色映射)
                random_password = secrets.token_urlsafe(16)
                user = User(
                    email=email,
                    is_active=True
                    # is_email_verified=userinfo.get('email_verified', True), # 字段已被移除
                    # email_verified_at=datetime.utcnow() if userinfo.get('email_verified', True) else None # 字段已被移除
                    # username=preferred_username, # 如果User模型有username字段
                )
                user.set_password(random_password) # 设置一个随机密码
                db.session.add(user)
                try:
                    db.session.commit()
                    logger.info(f"[OIDC Callback] 新用户 {email} 已创建")
                except IntegrityError as e:
                    db.session.rollback()
                    logger.error(f"[OIDC Callback] 创建用户 {email} 时发生 IntegrityError (可能并发导致): {e}", exc_info=True)
                    # 重新查询一次，可能已被其他请求创建
                    user = User.query.filter_by(email=email).first()
                    if not user:
                        return jsonify_response(success=False, message='创建用户失败，请重试', data=None, status_code=500)
                except Exception as e:
                    db.session.rollback()
                    logger.error(f"[OIDC Callback] 创建新用户 {email} 时发生数据库错误: {e}", exc_info=True)
                    return jsonify_response(success=False, message=f'创建新用户时发生数据库错误: {str(e)}', data=None, status_code=500) # Added data=None

        if not user.is_active:
            logger.warning(f"[OIDC Callback] 用户 {email} 存在但未激活，登录失败")
            return jsonify_response(success=False, message='您的账户未激活，请联系管理员', data=None, status_code=403) # Added data=None
        
        # 更新用户最后登录时间
        user.last_login = datetime.utcnow()
        db.session.commit()
        
        # 通过 g.auth_service 创建 JWT 令牌
        # logger.debug(f"[OIDC Callback] About to call g.auth_service.create_jwt_token for user {user.id}")
        internal_token = g.auth_service.generate_token(user) # 使用 g.auth_service

        # 将JWT令牌传递给前端
        # 方案A: 渲染一个页面，该页面用JS将token存入localStorage并跳转
        # 这通常是后端渲染模板的方式
        
        if user.is_admin:
            redirect_url_after_login = url_for('main.admin_dashboard_route', _external=False) # 管理员跳转到admin页面
        else:
            redirect_url_after_login = url_for('main.dashboard_route', _external=False) # 普通用户跳转到dashboard

        response_html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <title>Login Success</title>
            <script>
                try {{
                    localStorage.setItem('token', "{internal_token}");
                    localStorage.setItem('user', JSON.stringify({{"id": {user.id}, "email": "{user.email}", "is_admin": {str(user.is_admin).lower()}}}));
                    localStorage.setItem('lastLoginEmail', "{user.email}");
                    localStorage.setItem('auth_type', "sso"); // 标记为SSO登录
                    localStorage.setItem('id_token', "{token_response.get('id_token', '')}"); // 保存id_token用于登出
                    window.location.href = "{redirect_url_after_login}"; // 使用动态的重定向URL
                }} catch (e) {{
                    console.error("Error storing token or redirecting:", e);
                    document.body.innerHTML = "<h1>Login Error</h1><p>Could not store authentication token. Please enable cookies/localStorage and try again.</p>";
                }}
            </script>
        </head>
        <body>
            <p>Login successful, redirecting...</p>
        </body>
        </html>
        """
        return make_response(response_html)

    except Exception as e:
        # Authlib的 OAuthError 可能会在这里被捕获
        # 例如：MismatchedStateException, MissingCodeException, MissingTokenException
        logger.error(f"[OIDC Callback] 处理Keycloak回调时发生错误: {e}", exc_info=True)
        return jsonify_response(success=False, message=f'处理Keycloak回调时发生错误: {str(e)}', data=None, status_code=500) # Added data=None

@api.route('/auth/keycloak/logout')
@login_required # 使用您系统现有的 login_required 装饰器
def keycloak_logout():
    """用户从本系统和Keycloak登出 (SLO 部分)"""
    user_email = g.user.email # login_required 会把 user 放到 g 对象
    logger.info(f"[OIDC Logout] 用户 {user_email} 请求登出")

    # 1. 清除本系统会话 (前端通常通过删除localStorage中的token实现)
    # 后端能做的是让当前token失效（如果支持token吊销列表）或依赖其自然过期
    # 这里主要处理Keycloak的登出

    # 2. 重定向到Keycloak的登出端点
    # Keycloak的登出端点通常是 Issuer URL + /protocol/openid-connect/logout
    keycloak_issuer_url = current_app.config['OIDC_ISSUER_URL'] # was app.config
    logout_url = f"{keycloak_issuer_url}/protocol/openid-connect/logout"
    
    # 为了在Keycloak登出后重定向回您的应用，需要提供 id_token_hint 和 post_logout_redirect_uri
    # id_token_hint: 用户登录时获取到的ID Token。这部分比较复杂，因为通常后端不直接保存ID Token。
    # post_logout_redirect_uri: 登出后重定向回的URL，必须在Keycloak客户端配置中注册。
    
    # 简单登出：不处理 post_logout_redirect_uri 和 id_token_hint
    # 用户会被带到Keycloak的登出确认页面，或直接登出。
    # response = make_response(redirect(logout_url))
    # response.delete_cookie('session') # 如果有服务器端session cookie
    # return response
    
    # 更完整的登出需要前端配合或在session中存储id_token_hint
    # 这里我们只做简单重定向到Keycloak登出
    # 前端需要负责清除localStorage中的token
    return jsonify_response(
        success=True, 
        message='Logout initiated. Please clear local token storage.',
        data={'keycloak_logout_url': logout_url},
        status_code=200 # 明确指定 status_code
    )
