<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="ChatGPTPro俱乐部 - 尊贵AI拼车体验，多级拼车服务，专业技术支持，为您节省90%使用成本">
    <title data-i18n="welcome.page_title">ChatGPTPro俱乐部 - 尊享AI拼车服务</title>
    <link rel="icon" href="static/images/logo.png">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.8.1/font/bootstrap-icons.css">
    <style>
        :root {
            --primary-color: #2c3e50;
            --accent-color: #3498db;
            --success-color: #27ae60;
            --warning-color: #f39c12;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar {
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,.1);
        }
        
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 100px 0;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .pricing-card {
            transition: transform 0.3s;
            height: 100%;
        }
        
        .pricing-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0,0,0,.1);
        }
        
        .loading-spinner {
            display: none;
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        .testimonial-card {
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 1px solid rgba(52, 152, 219, 0.1);
            transition: all 0.3s ease;
        }
        
        .testimonial-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(0,0,0,.1);
        }
        
        .community-btn {
            background-color: rgba(52, 152, 219, 0.1);
            border: 1px solid rgba(52, 152, 219, 0.3);
            color: var(--accent-color);
            font-weight: 500;
            transition: all 0.3s ease;
            padding: 0.375rem 0.75rem;
        }
        
        .community-btn:hover {
            background-color: var(--accent-color);
            border-color: var(--accent-color);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
        }
        
        .community-btn i {
            font-size: 1.2rem;
        }
        
        .community-btn.telegram:hover {
            background-color: #0088cc;
            border-color: #0088cc;
        }
        
        .community-btn.discord:hover {
            background-color: #5865F2;
            border-color: #5865F2;
        }
    </style>
</head>
<body data-page-title-key="welcome.page_title">
    <!-- Loading Spinner -->
    <div class="loading-spinner">
        <div class="spinner-border text-primary" style="width: 3rem; height: 3rem;" role="status">
            <span class="visually-hidden" data-i18n="welcome.loading">加载中...</span>
        </div>
    </div>

    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="/">
                <img src="static/images/logo.png" data-i18n-alt="welcome.logo_alt" alt="Logo" height="30" class="d-inline-block align-text-top">
                <span data-i18n="welcome.brand_name">ChatGPTPro俱乐部</span>
            </a>
            <div class="navbar-nav ms-auto d-flex align-items-center">
                <!-- Community Links -->
                <div class="d-none d-md-flex me-3 gap-2">
                    <a href="https://t.me/gptproclub" target="_blank" rel="noopener noreferrer" class="btn community-btn telegram d-flex align-items-center">
                        <i class="bi bi-telegram me-1"></i> <span data-i18n="nav.tg_group">TG群</span>
                    </a>
                    <a href="https://t.me/chatgptpro_notification" target="_blank" rel="noopener noreferrer" class="btn community-btn telegram d-flex align-items-center">
                        <i class="bi bi-broadcast me-1"></i> <span data-i18n="nav.tg_channel">TG频道</span>
                    </a>
                    <a href="https://discord.gg/d6FnJKrekQ" target="_blank" rel="noopener noreferrer" class="btn community-btn discord d-flex align-items-center">
                        <i class="bi bi-discord me-1"></i> Discord
                    </a>
                </div>
                <!-- Language Switcher -->
                <div class="dropdown me-2">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="languageDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="bi bi-globe"></i> <span id="currentLanguageText" data-i18n="lang.zh_CN">中文</span>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="languageDropdown">
                        <li><a class="dropdown-item language-option" href="#" data-lang="zh-CN" data-i18n="lang.zh_CN">中文</a></li>
                        <li><a class="dropdown-item language-option" href="#" data-lang="en" data-i18n="lang.en">English</a></li>
                    </ul>
                </div>
                <a class="btn btn-primary" href="/login" id="login-btn">
                    <i class="bi bi-box-arrow-in-right"></i> <span data-i18n="welcome.login">登录</span>
                </a>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section">
        <div class="container">
            <h1 class="display-4 fw-bold mb-4" data-i18n="welcome.hero_title">欢迎来到 ChatGPTPro俱乐部</h1>
            <p class="lead mb-5" data-i18n="welcome.hero_subtitle">尊贵AI助手拼车体验，为您节省90%使用成本</p>
            <a href="#pricing" class="btn btn-light btn-lg me-3">
                <i class="bi bi-cart3"></i> <span data-i18n="welcome.view_plans">查看套餐</span>
            </a>
            <a href="/login" class="btn btn-outline-light btn-lg">
                <i class="bi bi-box-arrow-in-right"></i> <span data-i18n="welcome.login_now">立即登录</span>
            </a>
        </div>
    </section>

    <!-- Features Section -->
    <section class="py-5">
        <div class="container">
            <h2 class="text-center mb-5" data-i18n="welcome.why_choose_us">为什么选择我们</h2>
            <div class="row">
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-primary">
                        <i class="bi bi-shield-check"></i>
                    </div>
                    <h4 data-i18n="welcome.feature_safe_title">安全可靠</h4>
                    <p class="text-muted" data-i18n="welcome.feature_safe_desc">基于指纹浏览器技术，环境安全隔离，不降智不封号，原生官网账号</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-success">
                        <i class="bi bi-people"></i>
                    </div>
                    <h4 data-i18n="welcome.feature_flexible_title">灵活选择</h4>
                    <p class="text-muted" data-i18n="welcome.feature_flexible_desc">1人独享车、5人车、10人车等多种套餐，满足不同需求</p>
                </div>
                <div class="col-md-4 text-center mb-4">
                    <div class="feature-icon text-info">
                        <i class="bi bi-headset"></i>
                    </div>
                    <h4 data-i18n="welcome.feature_professional_title">专业服务</h4>
                    <p class="text-muted" data-i18n="welcome.feature_professional_desc">多位成员提供支持，快速响应，专业技术团队保障服务质量</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section class="py-5 bg-light" id="pricing">
        <div class="container">
            <h2 class="text-center mb-5" data-i18n="welcome.pricing_section_title">选择您的套餐</h2>
            <div class="row" id="pricing-cards">
                <!-- Pricing cards will be loaded here -->
                <div class="col-12 text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden" data-i18n="welcome.loading">加载中...</span>
                    </div>
                    <p class="mt-2" data-i18n="welcome.loading_plans">正在加载套餐信息...</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonial Section -->
    <section class="py-5">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-lg-8">
                    <div class="card testimonial-card shadow-sm">
                        <div class="card-body p-4">
                            <div class="d-flex align-items-start">
                                <i class="bi bi-quote text-primary" style="font-size: 2rem; line-height: 1;"></i>
                                <div class="ms-3">
                                    <p class="mb-3" style="font-style: italic;" data-i18n="welcome.testimonial_content">
                                        加入5人共享后，GPT-4o生成的参考图帮我突破了设计瓶颈，素材准备时间从2小时缩短到20分钟。图像质量好到让客户惊讶，整个设计流程更加流畅高效。
                                    </p>
                                    <p class="mb-0 text-end">
                                        <strong>— <span data-i18n="welcome.testimonial_author">陈总</span></strong>
                                        <span class="text-muted">，<span data-i18n="welcome.testimonial_role">创意公司合作伙伴</span></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-5 text-center">
        <div class="container">
            <h3 class="mb-4" data-i18n="welcome.cta_title">准备好开始了吗？</h3>
            <p class="lead mb-4" data-i18n="welcome.cta_subtitle">加入200+尊贵会员，享受高品质AI服务</p>
            <a href="/login" class="btn btn-primary btn-lg">
                <i class="bi bi-box-arrow-in-right"></i> <span data-i18n="welcome.join_now">立即加入</span>
            </a>
        </div>
    </section>

    <!-- Footer -->
    <footer class="py-4 bg-dark text-white text-center">
        <div class="container">
            <p class="mb-0" data-i18n="welcome.footer_copyright">&copy; 2025 ChatGPTPro俱乐部. All rights reserved.</p>
        </div>
    </footer>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/i18n.js"></script>
    
    <!-- Chatwoot 客服支持脚本 -->
    <script>
      // 获取当前登录用户信息
      var user = JSON.parse(localStorage.getItem('user') || '{}');
      var token = localStorage.getItem('token');
      
      // 获取当前页面语言设置
      var currentLang = localStorage.getItem('language') || 'zh-CN';
      
      // 映射到 Chatwoot 支持的语言代码
      var chatwootLangMap = {
        'zh-CN': 'zh_CN',
        'en': 'en'
      };
      var chatwootLocale = chatwootLangMap[currentLang] || 'zh_CN';
      
      // 设置全局 Chatwoot 设置（必须在脚本加载前设置）
      window.chatwootSettings = {
        hideMessageBubble: false,
        position: 'right',
        locale: chatwootLocale,
        type: 'standard',
        launcherTitle: ''  // 设置为空字符串，不显示标题
      };
      
      // 如果用户已登录，设置用户信息
      if (user && user.id && token) {
        window.chatwootSettings.user = {
          identifier: user.email,
          email: user.email,
          name: user.username || user.email.split('@')[0],
          identifier_hash: '',  // 如果需要验证，可以在后端生成 HMAC
          custom_attributes: {
            user_id: user.id,
            is_admin: user.is_admin || false,
            user_type: 'registered',
            platform: 'web',
            language: currentLang
          }
        };
      } else {
        // 访客用户的自定义属性
        window.chatwootSettings.custom_attributes = {
          user_type: 'visitor',
          platform: 'web',
          language: currentLang,
          page: 'welcome'
        };
      }
      
      // 加载 Chatwoot SDK
      (function(d,t) {
        var BASE_URL="https://support.chatgptpro.club";
        var g=d.createElement(t),s=d.getElementsByTagName(t)[0];
        g.src=BASE_URL+"/packs/js/sdk.js";
        g.defer = true;
        g.async = true;
        s.parentNode.insertBefore(g,s);
        g.onload=function(){
          window.chatwootSDK.run({
            websiteToken: 'rCAWY44bYvpjZm14wvdAUfzc',
            baseUrl: BASE_URL
          });
          
          // SDK 加载完成后，如果需要手动设置用户
          setTimeout(function() {
            if (window.$chatwoot && user && user.id) {
              // 尝试使用 setUser 方法
              if (window.$chatwoot.setUser) {
                window.$chatwoot.setUser(user.email, {
                  email: user.email,
                  name: user.username || user.email.split('@')[0],
                  identifier_hash: ''
                });
                
                // 设置自定义属性
                if (window.$chatwoot.setCustomAttributes) {
                  window.$chatwoot.setCustomAttributes({
                    user_id: user.id,
                    is_admin: user.is_admin || false,
                    user_type: 'registered',
                    platform: 'web',
                    language: currentLang
                  });
                }
              }
            }
          }, 1000); // 延迟1秒确保SDK完全初始化
        }
      })(document,"script");
    </script>
    
    <script>
        // Update language switcher display
        function updateLanguageSwitcherDisplay() {
            const currentLang = i18n.getCurrentLanguage();
            const langKey = currentLang === 'zh-CN' ? 'lang.zh_CN' : 'lang.en';
            const currentLanguageText = document.getElementById('currentLanguageText');
            if (currentLanguageText) {
                currentLanguageText.textContent = i18n.t(langKey);
            }
        }
        
        // Initialize language switcher
        function initLanguageSwitcher() {
            // Set initial display
            updateLanguageSwitcherDisplay();
            
            // Update HTML lang attribute
            const currentLang = i18n.getCurrentLanguage();
            document.documentElement.lang = currentLang;
            
            // Handle language switch
            document.querySelectorAll('.language-option').forEach(option => {
                option.addEventListener('click', function(e) {
                    e.preventDefault();
                    const lang = this.getAttribute('data-lang');
                    i18n.setLanguage(lang);
                    
                    // Update language switcher display
                    updateLanguageSwitcherDisplay();
                    
                    // Update meta description
                    const metaDesc = document.querySelector('meta[name="description"]');
                    if (metaDesc) {
                        metaDesc.content = i18n.t('welcome.meta_description');
                    }
                    
                    // Update page title
                    document.title = i18n.t('welcome.page_title');
                    
                    // Reload pricing cards with new language
                    loadPricingPlans();
                });
            });
        }
        
        // Listen for language changes and update switcher
        window.addEventListener('languageChanged', function() {
            updateLanguageSwitcherDisplay();
        });
        
        // Check if user is logged in and redirect accordingly
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize language switcher
            initLanguageSwitcher();
            
            // Set initial meta description and title based on current language
            const metaDesc = document.querySelector('meta[name="description"]');
            if (metaDesc) {
                metaDesc.content = i18n.t('welcome.meta_description');
            }
            document.title = i18n.t('welcome.page_title');
            
            const token = localStorage.getItem('token');
            const user = JSON.parse(localStorage.getItem('user') || '{}');
            
            if (token && user.id) {
                // User is logged in, redirect to appropriate page
                document.querySelector('.loading-spinner').style.display = 'block';
                if (user.is_admin) {
                    window.location.href = '/admin';
                } else {
                    window.location.href = '/dashboard';
                }
                return;
            }
            
            // Load pricing plans
            loadPricingPlans();
        });
        
        function loadPricingPlans() {
            fetch('/api/public/subscription-types')
                .then(response => response.json())
                .then(data => {
                    const container = document.getElementById('pricing-cards');
                    
                    if (data.success && data.data && data.data.types && data.data.types.length > 0) {
                        container.innerHTML = '';
                        
                        data.data.types.forEach(type => {
                            const col = document.createElement('div');
                            col.className = 'col-lg-4 col-md-6 mb-4';
                            
                            col.innerHTML = `
                                <div class="card pricing-card">
                                    <div class="card-header text-center">
                                        <h4>${type.name}</h4>
                                    </div>
                                    <div class="card-body">
                                        <h2 class="text-center mb-4"><span data-i18n="welcome.currency_symbol">¥</span>${type.price.toFixed(2)}<small class="text-muted" data-i18n="welcome.per_days" data-i18n-args='[${type.days}]'>/${type.days}天</small></h2>
                                        <ul class="list-unstyled">
                                            <li class="mb-2"><i class="bi bi-check-circle text-success"></i> <span data-i18n="welcome.max_devices" data-i18n-args='[${type.max_devices}]'>最多${type.max_devices}台设备</span></li>
                                            ${type.requirements ? type.requirements.split('\n').filter(line => line.trim()).map(line => 
                                                `<li class="mb-2"><i class="bi bi-check-circle text-success"></i> ${line.trim()}</li>`
                                            ).join('') : ''}
                                        </ul>
                                        <div class="text-center mt-4">
                                            <a href="/login" class="btn btn-outline-primary w-100">
                                                <i class="bi bi-box-arrow-in-right"></i> <span data-i18n="welcome.login_to_subscribe">登录后订阅</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            container.appendChild(col);
                        });
                        // 更新新添加元素的语言
                        i18n.updatePageLanguage();
                    } else {
                        container.innerHTML = `<div class="col-12 text-center"><p data-i18n="welcome.no_plans_available">暂无可用套餐</p></div>`;
                        i18n.updatePageLanguage(); // 更新新添加的元素
                    }
                })
                .catch(error => {
                    console.error('Failed to load pricing:', error);
                    document.getElementById('pricing-cards').innerHTML = 
                        `<div class="col-12 text-center"><p class="text-danger" data-i18n="welcome.loading_plans">加载套餐信息失败，请稍后重试</p></div>`;
                    i18n.updatePageLanguage(); // 更新新添加的元素
                });
        }
    </script>
</body>
</html>