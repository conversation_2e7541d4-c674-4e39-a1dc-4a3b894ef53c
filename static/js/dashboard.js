const loadingOverlay = document.getElementById('loading-overlay');
const loadingMessage = document.getElementById('loading-message');

function showLoading(message) {
    loadingMessage.textContent = message || i18n.t('common.processing');
    loadingOverlay.style.display = 'flex';
}

function hideLoading() {
    loadingOverlay.style.display = 'none';
}

// --- Section Navigation ---
const sidebarLinks = document.querySelectorAll('.sidebar .nav-link[data-section]');
const sections = document.querySelectorAll('.main-content .section-content');
const topbarTitle = document.getElementById('topbar-title');

// formatDateTime 函数已移至 utils.js，这里使用全局函数

function showSection(sectionId) {
    sections.forEach(section => {
        section.style.display = (section.id === `${sectionId}-section`) ? 'block' : 'none';
    });

    // Update active link in sidebar
    sidebarLinks.forEach(link => {
        link.classList.toggle('active', link.getAttribute('data-section') === sectionId);
    });

    // Update topbar title
    const activeLink = document.querySelector(`.sidebar .nav-link[data-section="${sectionId}"]`);
    if (activeLink) {
        const i18nKey = activeLink.querySelector('[data-i18n]')?.getAttribute('data-i18n');
        if (i18nKey) {
            topbarTitle.textContent = i18n.t(i18nKey);
        } else {
            topbarTitle.textContent = activeLink.textContent.trim();
        }
    }

    // Special actions when showing specific sections
    if (sectionId === 'pricing') {
        loadSubscriptionPlans(); // Load plans when pricing section is shown
    }
    if (sectionId === 'my-subscription') {
        loadSubscriptionInfo(); // Reload subscription info when section is shown
    }
     if (sectionId === 'dashboard') {
         // Optionally reload dashboard data if needed
         // loadDashboardOverview(); // You'd need to create this function
     }
     if (sectionId === 'device-management') {
        loadDevices(); // Load devices when section is shown
    }
}

sidebarLinks.forEach(link => {
    link.addEventListener('click', function(event) {
        event.preventDefault();
        const sectionId = this.getAttribute('data-section');
        showSection(sectionId);

        // Close sidebar on mobile after clicking a link
        const sidebar = document.getElementById('sidebarMenu');
        const backdrop = document.getElementById('sidebarBackdrop');
        if (window.innerWidth < 768) {
             sidebar.classList.remove('show');
             backdrop.classList.remove('show');
        }
    });
});

// Sidebar toggler for mobile
 const sidebarToggle = document.getElementById('sidebarToggle');
 const sidebarMenu = document.getElementById('sidebarMenu');
 const sidebarBackdrop = document.getElementById('sidebarBackdrop');
 
 if (sidebarToggle && sidebarMenu) {
     sidebarToggle.addEventListener('click', () => {
         sidebarMenu.classList.toggle('show');
         sidebarBackdrop.classList.toggle('show');
     });
 }
 
 // 点击遮罩层关闭侧边栏
 if (sidebarBackdrop) {
     sidebarBackdrop.addEventListener('click', () => {
         sidebarMenu.classList.remove('show');
         sidebarBackdrop.classList.remove('show');
     });
 }
 
// --- Original JavaScript Logic (Adapted) ---

// Popup payment simulation (or actual API call)
// 注意：这个函数现在已经被废弃，请使用 loadSubscriptionPlans 动态加载的按钮
function showPayment(planIdOrCode) {
    console.warn('showPayment function is deprecated, please use dynamically loaded subscription plan buttons');
    // 如果必须使用，建议先调用 API 获取订阅类型详情
    alert(i18n.t('pricing.select_plan'));
    showSection('pricing');
}

// DOMContentLoaded initialization
document.addEventListener('DOMContentLoaded', function() {
    // Dashboard页面仅供已登录用户使用
    loadUserInfo(); // Load user info first
    loadSubscriptionInfo(); // Load subscription info, which also updates overview
    updateUIForDirectLogin(); // Setup the direct login button area
    showSection('dashboard'); // Show dashboard by default
    
    // Add click listener for the guide overview card
    const guideCard = document.querySelector('.overview-card a[data-section="usage-guide"]');
    if (guideCard) {
        guideCard.addEventListener('click', (e) => {
            e.preventDefault();
            showSection('usage-guide');
        });
    }

    // Check for device confirmation message
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.has('device_confirmed')) {
        showAlert('success', i18n.t('device.new_device_confirmed'));
        // Clean the URL to prevent message showing on refresh
        history.replaceState(null, '', window.location.pathname);
    }
    
    // 页面卸载时停止轮询
    window.addEventListener('beforeunload', () => {
        stopDeviceQuotaPolling();
    });
    
    // Setup language switcher
    setupLanguageSwitcher();
});

// --- Helper function to handle unauthorized responses ---
function handleUnauthorizedResponse(response) {
    if (response.status === 401) {
        showAlert('warning', i18n.t('common.login_expired'));
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/login';
        return true; // Indicates that the response was handled
    }
    return false; // Indicates that the response was not a 401
}

// Logout
document.getElementById('logout-btn').addEventListener('click', async function() {
    const authType = localStorage.getItem('auth_type');
    const token = localStorage.getItem('token');
    
    if (authType === 'sso' && token) {
        // SSO登录，需要调用后端接口获取Keycloak登出URL
        try {
            const response = await fetch('/api/auth/keycloak/logout', {
                headers: {
                    'Authorization': 'Bearer ' + token
                }
            });
            
            if (response.ok) {
                const data = await response.json();
                
                // 先获取id_token，再清除本地存储
                const idToken = localStorage.getItem('id_token');
                
                // 清除本地存储
                localStorage.removeItem('token');
                localStorage.removeItem('user');
                localStorage.removeItem('auth_type');
                localStorage.removeItem('id_token');
                localStorage.removeItem('lastLoginEmail');
                
                // 重定向到Keycloak登出URL
                if (data.data && data.data.keycloak_logout_url) {
                    // 构建完整的登出URL，包含post_logout_redirect_uri
                    const logoutUrl = new URL(data.data.keycloak_logout_url);
                    
                    if (idToken) {
                        logoutUrl.searchParams.append('id_token_hint', idToken);
                    }
                    
                    // 设置登出后的重定向URL（返回到首页）
                    logoutUrl.searchParams.append('post_logout_redirect_uri', window.location.origin);
                    
                    window.location.href = logoutUrl.toString();
                } else {
                    // 如果无法获取Keycloak登出URL，至少返回首页
                    window.location.href = '/';
                }
            } else {
                // 请求失败，仍然清除本地存储并返回首页
                console.error('Logout request failed');
                localStorage.clear();
                window.location.href = '/';
            }
        } catch (error) {
            console.error('Error during logout:', error);
            localStorage.clear();
            window.location.href = '/';
        }
    } else {
        // 本地登录或无法确定登录类型，执行简单登出
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        localStorage.removeItem('auth_type');
        localStorage.removeItem('lastLoginEmail');
        window.location.href = '/';
    }
});

// Load user basic info
function loadUserInfo() {
    const token = localStorage.getItem('token');
    const user = JSON.parse(localStorage.getItem('user') || '{}');
    
    console.log("Dashboard loading. Token:", !!token, "User:", user);
    
    if (!token || !user.id) {
        console.log("Not logged in. Redirecting...");
        // Clear items just in case to ensure clean state before redirect
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        window.location.href = '/'; // Redirect to welcome page
        return;
    }
    
    // Display user info in topbar
    const userDisplay = document.getElementById('user-display');
    const userAvatar = document.getElementById('user-avatar');
    const dropdownUsername = document.getElementById('dropdown-username');
    const dropdownEmail = document.getElementById('dropdown-email');
    
    if (userDisplay) userDisplay.textContent = user.email || user.username || i18n.t('user.loading');
    
    // Update dropdown user info
    if (dropdownUsername) dropdownUsername.textContent = user.username || user.email || i18n.t('user.loading');
    if (dropdownEmail) dropdownEmail.textContent = user.email || '--';
    
    if (userAvatar) {
        if (user.email) {
            userAvatar.textContent = user.email.charAt(0).toUpperCase();
        } else if (user.username) {
            userAvatar.textContent = user.username.charAt(0).toUpperCase();
        } else {
            userAvatar.textContent = '?';
        }
    }
    
    // Set tooltip for user dropdown (shows on hover on desktop)
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown) {
        userDropdown.setAttribute('title', user.email || user.username || '');
    }
    
    // Optional: Load detailed user info for profile section if needed
    // loadUserProfileDetails(token); // Create this function if you add a profile section
}

 /* // Example: Load detailed profile info (if profile section exists)
  function loadUserProfileDetails(token) {
    const userInfoDetails = document.getElementById('user-info-details');
    if (!userInfoDetails) return;

     fetch('/api/users/me', { headers: { 'Authorization': `Bearer ${token}` } })
     .then(response => {
         if (handleUnauthorizedResponse(response)) return null; // Check for 401
         return response.json();
     })
     .then(data => {
         if (!data) return; // Handled by unauthorized check

         if (data.success && data.user) {
            const user = data.user;
             userInfoDetails.innerHTML = `
                                <p><strong>${i18n.t('user.username')}:</strong> ${user.username}</p>
                                <p><strong>${i18n.t('user.email')}:</strong> ${user.email}</p>
                                <p><strong>${i18n.t('user.created_at')}:</strong> ${new Date(user.created_at).toLocaleDateString()}</p>
                                <p><strong>${i18n.t('user.status')}:</strong> ${user.is_active ? `<span class="badge bg-success">${i18n.t('status.active')}</span>` : `<span class="badge bg-danger">${i18n.t('status.inactive')}</span>`}</p>
            `;
         } else {
             userInfoDetails.innerHTML = `<p class="text-danger">${i18n.t('user.load_failed')}</p>`;
        }
    })
    .catch(error => {
         console.error('Failed to get detailed user info:', error);
         userInfoDetails.innerHTML = `<p class="text-danger">${i18n.t('user.load_error')}</p>`;
     });
 }
 */

// Load subscription info
function loadSubscriptionInfo() {
    const token = localStorage.getItem('token');
    const subscriptionDetailContainer = document.getElementById('subscription-detail');
    const subscriptionActionsContainer = document.getElementById('subscription-actions');
    const overviewDevices = document.getElementById('overview-devices'); // For device count in overview

    // Show loading state specifically for subscription details
    if (subscriptionDetailContainer) {
        subscriptionDetailContainer.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border spinner-border-sm text-secondary" role="status"></div>
                <span class="ms-2 text-muted">${i18n.t('subscription.loading')}</span>
            </div>`;
    }
    if (subscriptionActionsContainer) subscriptionActionsContainer.innerHTML = ''; // Clear actions
    if (overviewDevices) overviewDevices.textContent = '--'; // Set loading state for devices card in overview

    // Fetch both subscription and device data in parallel
    Promise.all([
        fetch('/api/subscriptions/current', { headers: { 'Authorization': `Bearer ${token}` } }).then(res => {
            if (handleUnauthorizedResponse(res)) return null;
            return res.json();
        }),
        fetch('/api/devices', { headers: { 'Authorization': `Bearer ${token}` } }).then(res => {
            if (handleUnauthorizedResponse(res)) return null;
            return res.json();
        })
    ])
    .then(([subscriptionAPIResponse, deviceAPIResponse]) => {
        // If both are null (e.g. due to 401), exit early.
        if (!subscriptionAPIResponse && !deviceAPIResponse) return;

        console.log('Subscription API response:', subscriptionAPIResponse);
        console.log('Device API response:', deviceAPIResponse);

        let currentDeviceCount = 0;
        // Process device data first if available and successful
        if (deviceAPIResponse && deviceAPIResponse.success && deviceAPIResponse.data && Array.isArray(deviceAPIResponse.data.devices)) {
            currentDeviceCount = deviceAPIResponse.data.devices.length;
        } else if (deviceAPIResponse && !deviceAPIResponse.success) {
            console.warn("Failed to load device count:", deviceAPIResponse.message);
            // Potentially show a small error or "--" for device count if critical,
            // but for now, it will default to 0 if the API call fails.
        }


        const sub = (subscriptionAPIResponse && subscriptionAPIResponse.success && subscriptionAPIResponse.data) ? subscriptionAPIResponse.data.subscription : null;
        let deviceUsageText = '0 / 0'; // Default for overview
        let overviewStatusClass = 'secondary'; // Default for overview status color

        if (sub) { // Checks if sub is not null (i.e., API call was successful and subscription data (even "no subscription") is present)
            // Determine overview status class
            if (sub.status_text === i18n.t('status.no_subscription') || sub.is_expired) {
                overviewStatusClass = 'danger';
                // Don't show device quota for expired subscriptions
                deviceUsageText = i18n.t('status.expired');
            } else if (sub.status_text === i18n.t('status.active')) {
                overviewStatusClass = 'success';
                deviceUsageText = `${currentDeviceCount} / ${sub.max_devices || 0}`;
            }
            
            // Update Dashboard Overview
            updateDashboardOverview(sub.expiry_date_for_dashboard, sub.status_text, overviewStatusClass, deviceUsageText);

            // Update "My Subscription" section
            if (subscriptionDetailContainer) {
                let startDateDisplay = 'N/A';
                if (sub.raw_start_date_utc_iso) {
                    startDateDisplay = formatDateTime(sub.raw_start_date_utc_iso);
                }
                
                let endDateDisplay = 'N/A';
                if (sub.raw_end_date_utc_iso) {
                    endDateDisplay = formatDateTime(sub.raw_end_date_utc_iso);
                }

                subscriptionDetailContainer.innerHTML = `
                    <p><strong>${i18n.t('subscription.current_plan')}:</strong> <span class="fw-bold">${sub.plan_name || 'N/A'}</span></p>
                    <p><strong>${i18n.t('subscription.status')}:</strong> <span class="badge bg-${overviewStatusClass}">${sub.status_text}</span></p>
                    <p><strong>${i18n.t('subscription.start_date')}:</strong> ${startDateDisplay}</p>
                    <p><strong>${i18n.t('subscription.end_date')}:</strong> ${endDateDisplay}</p>
                    <p><strong>${i18n.t('subscription.device_limit')}:</strong> ${sub.max_devices || (sub.status_text === i18n.t('status.no_subscription') ? 0 : 'N/A')} ${i18n.t('subscription.devices_unit')}</p>
                    <p><strong>${i18n.t('subscription.instance_name')}:</strong> <span id="currentInstanceName" class="fw-bold">${sub.subscription_instance_name ? sub.subscription_instance_name  : i18n.t('subscription.none')}</span></p>
                    <p><strong>${i18n.t('subscription.instance_desc')}:</strong> <span id="currentInstanceDescription">${sub.subscription_instance_description || i18n.t('subscription.none')}</span></p>
                `;
            }

            // Update subscription actions (e.g., renew button)
            if (subscriptionActionsContainer) {
                if (sub.status_text === i18n.t('status.no_subscription') || sub.is_expired) {
                    subscriptionActionsContainer.innerHTML = `
                        <button class="btn btn-primary" onclick="showSection('pricing')">
                            <i class="bi bi-cart3"></i> ${i18n.t('subscription.buy_now')}
                        </button>
                    `;
                } else {
                     subscriptionActionsContainer.innerHTML = `
                        <button class="btn btn-success" onclick="renewCurrentSubscription()">
                            <i class="bi bi-arrow-repeat"></i> ${i18n.t('subscription.renew_now')}
                        </button>
                    `;
                }
            }
             // Update device usage message based on subscription
            const deviceUsageMessageContentSpan = document.getElementById('device-usage-message-content');
            if (deviceUsageMessageContentSpan) {
                if (sub.status_text === i18n.t('status.active') && !sub.is_expired) {
                    deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${i18n.t('adspower.device_usage_active', sub.plan_name || i18n.t('subscription.current_plan'), sub.subscription_instance_name ? `${i18n.t('common.instance')}: ${sub.subscription_instance_name}` : i18n.t('subscription.common'), sub.max_devices || 0, currentDeviceCount)}`;
                } else if (sub.is_expired) {
                    deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${i18n.t('adspower.device_usage_expired')}`;
                } else { // No subscription
                    deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${i18n.t('adspower.device_usage_none')}`;
                }
            }

        } else { // Subscription API call failed or structure was unexpected
            const message = (subscriptionAPIResponse && subscriptionAPIResponse.message) ? subscriptionAPIResponse.message : i18n.t('common.unable_load');
            updateDashboardOverview(i18n.t('status.no_subscription'), i18n.t('status.error'), 'danger', `${currentDeviceCount} / 0`); // Show current device count if available
            if (subscriptionDetailContainer) {
                subscriptionDetailContainer.innerHTML = `
                <div class="alert alert-warning">
                    <strong>${i18n.t('subscription.no_subscription_tip')}:</strong> ${message}
                </div>
                <p><strong>${i18n.t('subscription.instance_name')}:</strong> <span id="currentInstanceName" class="fw-bold">${i18n.t('subscription.none')}</span></p>
                <p><strong>${i18n.t('subscription.instance_desc')}:</strong> <span id="currentInstanceDescription">${i18n.t('subscription.none')}</span></p>
                `;
            }
            if (subscriptionActionsContainer) {
                subscriptionActionsContainer.innerHTML = `
                    <button class="btn btn-primary" onclick="showSection('pricing')">
                        <i class="bi bi-cart3"></i> ${i18n.t('subscription.view_plans')}
                    </button>
                `;
            }
            const deviceUsageMessageContentSpan = document.getElementById('device-usage-message-content');
            if (deviceUsageMessageContentSpan) {
                 deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${message} ${i18n.t('adspower.device_usage_none')}`;
            }
        }
    })
    .catch(error => {
        console.error('Failed to get subscription or device info:', error);
        updateDashboardOverview(i18n.t('status.error'), i18n.t('status.error'), 'danger', i18n.t('common.loading_error'));
        if (subscriptionDetailContainer) {
            subscriptionDetailContainer.innerHTML = `
            <div class="alert alert-danger">
                <strong>${i18n.t('common.api_error')}:</strong> ${error.message || i18n.t('common.server_error')}
            </div>
            <p><strong>${i18n.t('subscription.instance_name')}:</strong> <span id="currentInstanceName" class="fw-bold">${i18n.t('subscription.none')}</span></p>
            <p><strong>${i18n.t('subscription.instance_desc')}:</strong> <span id="currentInstanceDescription">${i18n.t('subscription.none')}</span></p>
            `;
        }
        if (subscriptionActionsContainer) {
            subscriptionActionsContainer.innerHTML = `
                <button class="btn btn-primary" onclick="showSection('pricing')">
                     <i class="bi bi-cart3"></i> ${i18n.t('subscription.view_plans')}
                </button>
            `;
        }
        const deviceUsageMessageContentSpan = document.getElementById('device-usage-message-content');
        if (deviceUsageMessageContentSpan) {
             deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${i18n.t('common.check_network')}`;
        }
    });
}

// Helper to format seconds into days, hours, minutes string
function formatSecondsToDHMS(totalSeconds) {
    if (totalSeconds <= 0) return i18n.t('common.already_ended');
    const days = Math.floor(totalSeconds / (3600 * 24));
    const hours = Math.floor((totalSeconds % (3600 * 24)) / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    
    let parts = [];
    if (days > 0) parts.push(days + i18n.t('common.days'));
    if (hours > 0) parts.push(hours + i18n.t('common.hours'));
    if (minutes > 0) parts.push(minutes + i18n.t('common.minutes'));
    
    if (parts.length === 0) {
        if (totalSeconds > 0) return i18n.t('common.less_than_minute');
        return i18n.t('common.already_ended'); // Should not happen if totalSeconds > 0 checked first
    }
    return parts.join('');
}

// Helper to update overview cards
function updateDashboardOverview(expiry, status, statusClass, deviceUsage) {
    const overviewExpiry = document.getElementById('overview-expiry');
    const overviewStatus = document.getElementById('overview-status');
    const overviewDevices = document.getElementById('overview-devices');

    if (overviewExpiry) overviewExpiry.textContent = expiry;
    if (overviewStatus) {
        overviewStatus.textContent = status;
        overviewStatus.className = `title status-${statusClass}`; 
    }
    if (overviewDevices) overviewDevices.textContent = deviceUsage;
}

// Update UI for Direct Login (replaces profile list)
function updateUIForDirectLogin() {
        const profilesContainer = document.getElementById('profiles-container');
    const profileActionsContainer = document.getElementById('profile-actions');

    if (profilesContainer) {
        profilesContainer.innerHTML = `
            <div class="text-center">
                <button id="direct-login-btn" class="btn btn-primary btn-lg">
                        <i class="bi bi-box-arrow-in-right"></i> ${i18n.t('adspower.login_button')}
                </button>
                <div class="mt-3" id="login-status-container" style="display:none;"></div>
            </div>`;

        // Add event listener for the new button
        const directLoginBtn = document.getElementById('direct-login-btn');
         if(directLoginBtn) {
            directLoginBtn.addEventListener('click', requestDirectAdspowerLogin);
         } else {
             console.error("Cannot find #direct-login-btn");
         }
    } else {
         console.error("Cannot find #profiles-container");
    }

    // Update or hide the old "actions" area below profiles
    if (profileActionsContainer) {
         profileActionsContainer.innerHTML = `
            <div class="alert alert-light text-center border mt-4">
                 ${i18n.t('adspower.click_hint')}
            </div>
        `;
         // Or simply: profileActionsContainer.style.display = 'none';
    }
}

// 全局变量，用于存储设备额度轮询定时器
let deviceQuotaPollingInterval = null;
let deviceQuotaPollingStartTime = null;
const POLLING_INTERVAL = 5000; // 每5秒轮询一次
const MAX_POLLING_DURATION = 180000; // 最多轮询3分钟

// 开始设备额度轮询
function startDeviceQuotaPolling() {
    // 清除可能存在的旧轮询
    stopDeviceQuotaPolling();
    
    // 记录开始时间
    deviceQuotaPollingStartTime = Date.now();
    
    // 获取初始设备数量
    const token = localStorage.getItem('token');
    let initialDeviceCount = null;
    
    // 先获取一次当前设备数量
    fetch('/api/devices', { headers: { 'Authorization': `Bearer ${token}` } })
        .then(res => res.json())
        .then(data => {
            if (data.success && data.data && Array.isArray(data.data.devices)) {
                initialDeviceCount = data.data.devices.length;
                console.log(`Starting device quota polling, initial device count: ${initialDeviceCount}`);
            }
        });
    
    // 添加轮询状态提示
    const pollingIndicator = document.createElement('div');
    pollingIndicator.id = 'device-quota-polling-indicator';
    pollingIndicator.className = 'alert alert-info mt-2 py-1 small d-flex justify-content-between align-items-center';
    pollingIndicator.innerHTML = `
        <span>
            <i class="bi bi-arrow-repeat spin me-1"></i> ${i18n.t('device.monitoring')}
            <span class="text-muted ms-2">${i18n.t('device.monitoring_interval')}</span>
        </span>
        <button class="btn btn-sm btn-outline-secondary py-0 px-2" onclick="stopDeviceQuotaPolling()">
            <i class="bi bi-x-circle"></i> ${i18n.t('device.stop_monitoring')}
        </button>
    `;
    
    // 添加旋转动画样式
    if (!document.getElementById('polling-spin-style')) {
        const style = document.createElement('style');
        style.id = 'polling-spin-style';
        style.textContent = `
            @keyframes spin {
                from { transform: rotate(0deg); }
                to { transform: rotate(360deg); }
            }
            .spin {
                display: inline-block;
                animation: spin 2s linear infinite;
            }
        `;
        document.head.appendChild(style);
    }
    
    const statusContainer = document.getElementById('login-status-container');
    if (statusContainer) {
        statusContainer.appendChild(pollingIndicator);
    }
    
    // 开始轮询
    deviceQuotaPollingInterval = setInterval(() => {
        // 检查是否超时
        if (Date.now() - deviceQuotaPollingStartTime > MAX_POLLING_DURATION) {
            console.log('Device quota polling timeout, stopping polling');
            stopDeviceQuotaPolling();
            return;
        }
        
        // 获取最新设备数量
        fetch('/api/devices', { headers: { 'Authorization': `Bearer ${token}` } })
            .then(res => res.json())
            .then(data => {
                if (data.success && data.data && Array.isArray(data.data.devices)) {
                    const currentDeviceCount = data.data.devices.length;
                    console.log(`Polling - current device count: ${currentDeviceCount}, initial device count: ${initialDeviceCount}`);
                    
                    // 如果设备数量增加了，说明用户已经登录成功
                    if (initialDeviceCount !== null && currentDeviceCount > initialDeviceCount) {
                        console.log('New device detected, refreshing device quota display');
                        refreshDeviceQuota();
                        stopDeviceQuotaPolling();
                        showAlert('success', i18n.t('device.new_device_detected'));
                    }
                }
            })
            .catch(error => {
                console.error('Failed to poll device count:', error);
            });
    }, POLLING_INTERVAL);
}

// 停止设备额度轮询
function stopDeviceQuotaPolling() {
    if (deviceQuotaPollingInterval) {
        clearInterval(deviceQuotaPollingInterval);
        deviceQuotaPollingInterval = null;
    }
    
    // 移除轮询指示器
    const indicator = document.getElementById('device-quota-polling-indicator');
    if (indicator) {
        indicator.remove();
    }
    
    deviceQuotaPollingStartTime = null;
    console.log('Device quota polling stopped');
}

// Request direct login URL from backend
function requestDirectAdspowerLogin() {
    // 先确认用户已准备好
    const hasAdsPower = localStorage.getItem('hasInstalledAdsPower');
    if (!hasAdsPower) {
        const userConfirmed = confirm(
            i18n.t('adspower.confirm_install')
        );
        
        if (!userConfirmed) {
            window.open('https://www.adspower.net/download', '_blank');
            return;
        }
        
        // 记住用户已确认安装
        localStorage.setItem('hasInstalledAdsPower', 'true');
    }
    
    const token = localStorage.getItem('token');
    const directLoginBtn = document.getElementById('direct-login-btn');
    const statusContainer = document.getElementById('login-status-container');

    if (!directLoginBtn || !statusContainer) {
        console.error("Login button or status container not found!");
        showAlert('danger', i18n.t('common.page_error'));
        return;
    }

    // Show inline loading state within the card
    directLoginBtn.disabled = true;
    directLoginBtn.innerHTML = `<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ${i18n.t('adspower.processing')}`; // Update button text
    statusContainer.style.display = 'block'; // Ensure container is visible
    statusContainer.innerHTML = `
        <div class="alert alert-info py-2 d-flex align-items-center">
            <div class="spinner-border spinner-border-sm text-primary me-2" role="status"></div>
            <div>
                ${i18n.t('adspower.allocating')} 
                <small class="d-block text-muted">${i18n.t('adspower.dont_refresh')}</small>
            </div>
        </div>`;

    // --- API Call ---
    fetch('/api/adspower/direct-login', {
        method: 'POST',
        headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        }
    })
    .then(response => {
        if (handleUnauthorizedResponse(response)) return null;
        return response.json();
    }) 
    .then(responseData => { // Renamed to responseData for clarity
        if (!responseData) return; 
        directLoginBtn.innerHTML = `<i class="bi bi-box-arrow-in-right"></i> ${i18n.t('adspower.login_button')}`;
        directLoginBtn.disabled = false;
        statusContainer.style.display = 'block';

        if (responseData.success && responseData.data && responseData.data.login_url) {
            statusContainer.innerHTML = `
                <div class="alert alert-success">
                    <strong>${i18n.t('adspower.login_ready')}</strong>
                    <p class="mt-2 mb-2">${i18n.t('adspower.login_desc')}</p>
                    <a href="${responseData.data.login_url}" class="btn btn-success mt-1"> 
                        <i class="bi bi-box-arrow-in-right"></i> ${i18n.t('adspower.open_login')}
                    </a>
                     <p class="mt-2 mb-0 small text-muted">${i18n.t('adspower.login_help').replace(i18n.t('nav.usage_guide'), `<a href="#" data-section="usage-guide" onclick="event.preventDefault(); showSection('usage-guide');">${i18n.t('nav.usage_guide')}</a>`)}</p>
                </div>`;
            
            // 开始轮询设备额度变化
            startDeviceQuotaPolling();
        } else {
            let failureMessage = `<strong>${i18n.t('adspower.login_failed')}</strong> ${responseData.message || i18n.t('common.data_format_error')}`;
            if (responseData.success && responseData.data && !responseData.data.login_url) {
                failureMessage = `<strong>${i18n.t('adspower.login_failed')}</strong> ${responseData.message || i18n.t('common.backend_no_url')}`;
            }
            let possibleReasons = '';

            const errorCode = responseData.data ? responseData.data.error_code : responseData.error_code; // Check in data first, then top level for compatibility
            
            // 根据错误代码显示更具体的信息
            if (errorCode === 'account_in_use') {
                // 账号被其他用户使用中
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-info-circle"></i> ${i18n.t('error.account_in_use')}</p>`;
            } else if (errorCode === 'all_accounts_busy') {
                // 所有账号都被占用
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-clock-history"></i> ${i18n.t('error.all_accounts_busy')}</p>`;
            } else if (errorCode === 'no_adspower_account_in_instance') {
                // 资源组内无可用账号
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-exclamation-triangle"></i> ${i18n.t('error.no_adspower_account')}</p>`;
            } else if (errorCode === 'account_full_at_final_check') {
                // 账号在最终检查时已满员
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-people-fill"></i> ${i18n.t('error.account_full')}</p>`;
            } else if (errorCode === 'DEVICE_LIMIT_REACHED') {
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-device-hdd"></i> ${i18n.t('error.device_limit')}</p>`;
            } else if (errorCode === 'no_subscription') {
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-cart3"></i> ${i18n.t('error.no_subscription_error')}</p>`;
            } else if (errorCode === 'account_verification_failed') {
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-shield-exclamation"></i> ${i18n.t('error.verification_failed')}</p>`;
            } else if (errorCode === 'session_creation_failed') {
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-x-circle"></i> ${i18n.t('error.session_failed')}</p>`;
            } else {
                // 未知错误代码，显示通用提示
                possibleReasons = `<p class="mt-2 small"><i class="bi bi-question-circle"></i> ${i18n.t('error.unknown')}</p>`;
            }

            statusContainer.innerHTML = `
                <div class="alert alert-danger">
                    ${failureMessage}
                    ${possibleReasons}
                    <button class="btn btn-sm btn-outline-danger mt-2 ms-2" onclick="requestDirectAdspowerLogin()">
                        <i class="bi bi-arrow-clockwise"></i> ${i18n.t('adspower.retry')}
                    </button>
                </div>`;
        }
    })
    .catch(error => {
        // Restore button state FIRST on error too
        directLoginBtn.innerHTML = `<i class="bi bi-box-arrow-in-right"></i> ${i18n.t('adspower.login_button')}`;
        directLoginBtn.disabled = false;
        // THEN update status container
        statusContainer.style.display = 'block'; // Keep container visible
        console.error('Failed to create login request:', error);
        statusContainer.innerHTML = `
            <div class="alert alert-danger">
                <strong>${i18n.t('adspower.network_error')}</strong> ${i18n.t('adspower.network_error_desc')}
                <button class="btn btn-sm btn-outline-danger mt-2 ms-2" onclick="requestDirectAdspowerLogin()">
                    <i class="bi bi-arrow-clockwise"></i> ${i18n.t('adspower.retry')}
                </button>
            </div>`;
    });
}

// 仅刷新设备额度信息
function refreshDeviceQuota(showSuccessAlert = true) {
    const token = localStorage.getItem('token');
    const overviewDevices = document.getElementById('overview-devices');
    const deviceQuotaStats = document.getElementById('device-quota-stats');
    const deviceQuotaProgressBar = document.getElementById('device-quota-progress-bar');
    const deviceUsageMessageContentSpan = document.getElementById('device-usage-message-content');
    
    // 显示加载状态
    if (overviewDevices) overviewDevices.innerHTML = `<span class="spinner-border spinner-border-sm"></span> ${i18n.t('common.loading')}`;
    if (deviceQuotaStats) deviceQuotaStats.innerHTML = `<span class="spinner-border spinner-border-sm"></span> ${i18n.t('common.loading')}`;
    
    // 并行获取订阅和设备信息
    Promise.all([
        fetch('/api/subscriptions/current', { headers: { 'Authorization': `Bearer ${token}` } }).then(res => {
            if (handleUnauthorizedResponse(res)) return null;
            return res.json();
        }),
        fetch('/api/devices', { headers: { 'Authorization': `Bearer ${token}` } }).then(res => {
            if (handleUnauthorizedResponse(res)) return null;
            return res.json();
        })
    ])
    .then(([subscriptionData, deviceData]) => {
        if (!subscriptionData || !deviceData) return;
        
        const sub = subscriptionData.success && subscriptionData.data ? subscriptionData.data.subscription : null;
        const currentDeviceCount = deviceData.success && deviceData.data && Array.isArray(deviceData.data.devices) ? 
                                   deviceData.data.devices.length : 0;
        
        if (sub) {
            const deviceUsageText = `${currentDeviceCount} / ${sub.max_devices || 0}`;
            
            // 更新概览卡片
            if (overviewDevices) overviewDevices.textContent = deviceUsageText;
            
            // 更新设备管理页面的统计信息
            if (deviceQuotaStats) deviceQuotaStats.textContent = deviceUsageText;
            
            // 更新进度条
            if (deviceQuotaProgressBar && sub.max_devices > 0) {
                const usagePercentage = (currentDeviceCount / sub.max_devices) * 100;
                deviceQuotaProgressBar.style.width = `${usagePercentage}%`;
                deviceQuotaProgressBar.setAttribute('aria-valuenow', usagePercentage);
                
                // 根据使用率调整颜色
                deviceQuotaProgressBar.className = 'progress-bar';
                if (usagePercentage >= 100) {
                    deviceQuotaProgressBar.classList.add('bg-danger');
                } else if (usagePercentage >= 80) {
                    deviceQuotaProgressBar.classList.add('bg-warning');
                } else {
                    deviceQuotaProgressBar.classList.add('bg-success');
                }
            }
            
            // 更新设备使用说明 - 根据订阅状态显示不同的消息
            if (deviceUsageMessageContentSpan) {
                if (sub.status_text === i18n.t('status.active') && !sub.is_expired) {
                    deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${i18n.t('adspower.device_usage_active', sub.plan_name || i18n.t('subscription.current_plan'), sub.subscription_instance_name ? `${i18n.t('common.instance')}: ${sub.subscription_instance_name}` : i18n.t('subscription.common'), sub.max_devices || 0, currentDeviceCount)}`;
                } else if (sub.is_expired) {
                    deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${i18n.t('adspower.device_usage_expired')}`;
                } else {
                    deviceUsageMessageContentSpan.innerHTML = `<strong>${i18n.t('adspower.device_usage')}:</strong> ${i18n.t('adspower.device_usage_none')}`;
                }
            }
            
            // 根据参数决定是否显示成功提示
            if (showSuccessAlert) {
                showAlert('success', i18n.t('device.quota_refreshed'));
            }
        }
    })
    .catch(error => {
        console.error('Failed to refresh device quota:', error);
        if (overviewDevices) overviewDevices.textContent = i18n.t('common.loading_error');
        if (deviceQuotaStats) deviceQuotaStats.textContent = i18n.t('common.loading_error');
        if (showSuccessAlert) {
            showAlert('danger', i18n.t('device.quota_refresh_failed'));
        }
    });
}

// Show alert messages
function showAlert(type, message) {
    const alertContainer = document.querySelector('.main-content'); // Insert at top of main content
    if (!alertContainer) return;

    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show mt-0 mb-3`; // Adjust margins as needed
    alertDiv.setAttribute('role', 'alert');
    alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    alertContainer.insertBefore(alertDiv, alertContainer.firstChild);
    
    // Auto-dismiss after 5 seconds
    setTimeout(() => {
        bootstrap.Alert.getOrCreateInstance(alertDiv)?.close();
    }, 5000);
}

// Load Subscription Plans for Pricing Section
function loadSubscriptionPlans() {
    const plansContainer = document.getElementById('subscription-plans-container');
    const token = localStorage.getItem('token');

    if (!plansContainer) return;

     // Show loading state for plans
     plansContainer.innerHTML = `
         <div class="col-12 text-center my-5">
             <div class="spinner-border text-primary" role="status">
                 <span class="visually-hidden">${i18n.t('common.loading')}</span>
             </div>
             <p class="mt-2">${i18n.t('pricing.loading')}</p>
         </div>`;


    if (!token) {
        plansContainer.innerHTML = `
            <div class="col-12">
                <div class="alert alert-warning">
                    <i class="bi bi-exclamation-triangle"></i> ${i18n.t('pricing.login_required')}
                </div>
            </div>`;
        return;
    }

    fetch('/api/subscription-types', { headers: { 'Authorization': `Bearer ${token}` } })
        .then(response => {
            if (handleUnauthorizedResponse(response)) return null;
            return response.json();
        })
        .then(data => {
            if (!data) return; // Handled by unauthorized check

            if (data.success && data.data && data.data.types && data.data.types.length > 0) {
                const publicTypes = data.data.types.filter(type => type.is_public);

                if (publicTypes.length === 0) {
                    plansContainer.innerHTML = `
                        <div class="col-12">
                            <div class="alert alert-secondary text-center">
                                <i class="bi bi-info-circle"></i> ${i18n.t('pricing.no_plans')}
                            </div>
                        </div>`;
                    return;
                }

                plansContainer.innerHTML = ''; // Clear loading/previous content

                publicTypes.forEach(type => {
                    const finalPrice = type.price.toFixed(2);

                    const planCard = document.createElement('div');
                    planCard.className = 'col-lg-4 col-md-6 mb-4 d-flex align-items-stretch'; // Use flex for equal height cards
                    planCard.innerHTML = `
                        <div class="card h-100">
                            <div class="card-header">
                                <h5 class="mb-0">${type.name}</h5>
                            </div>
                            <div class="card-body d-flex flex-column">
                                <div class="mb-3 text-center">
                                    <h2 class="card-title pricing-card-title mb-0">
                                        ¥${finalPrice}
                                    </h2>
                                    <div class="text-muted mb-1">${i18n.t('pricing.per_days', type.days)}</div>
                                    
                                </div>
                                 <p class="text-muted small text-center">${type.description || i18n.t('pricing.standard_plan')}</p>
                                <ul class="list-unstyled mt-3 mb-4 flex-grow-1">
                                    <li><i class="bi bi-check-circle-fill text-success me-2"></i>${i18n.t('pricing.max_devices', type.max_devices)}</li>
                                    ${type.requirements ? type.requirements.split('\n').filter(line => line.trim()).map(line => 
                                        `<li><i class="bi bi-check-circle-fill text-success me-2"></i>${line.trim()}</li>`
                                    ).join('') : ''}
                                </ul>
                                <div class="mt-auto">
                                    <button class="btn btn-outline-primary w-100 purchase-subscription-btn"
                                            data-plan-id="${type.id}"
                                            data-plan-code="${type.code}" 
                                            data-plan-price="${finalPrice}"
                                            data-plan-name="${type.name}">
                                        ${i18n.t('pricing.subscribe_now')}
                                    </button>
                                </div>
                            </div>
                        </div>`;
                    plansContainer.appendChild(planCard);
                });

                // Add event listeners to new buttons
                document.querySelectorAll('.purchase-subscription-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const planId = this.getAttribute('data-plan-id'); 
                        const price = parseFloat(this.getAttribute('data-plan-price'));
                        const name = this.getAttribute('data-plan-name');
                        createPaymentOrder(planId, price, name); 
                    });
                });

            } else if (data.success && (!data.data || !data.data.types || data.data.types.length === 0)) {
                 // Add specific handling for empty types array
                 plansContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-info text-center">
                            <i class="bi bi-info-circle"></i> ${i18n.t('pricing.no_plans')}
                        </div>
                    </div>`;
            }
            else {
                plansContainer.innerHTML = `
                    <div class="col-12">
                        <div class="alert alert-warning text-center">
                            <i class="bi bi-exclamation-triangle"></i> ${i18n.t('common.unable_load')} ${data.message || ''}
                        </div>
                    </div>`;
            }
        })
        .catch(error => {
            console.error('Failed to get subscription plans:', error);
            plansContainer.innerHTML = `
                <div class="col-12">
                    <div class="alert alert-danger text-center">
                        <i class="bi bi-exclamation-triangle-fill"></i> ${i18n.t('common.api_error')}: ${error.message || i18n.t('common.network_error')}
                    </div>
                </div>`;
        });
}

// Create Payment Order and Redirect (or handle payment flow)
async function createPaymentOrder(planId, price, planName) {
    const token = localStorage.getItem('token');
    if (!token) {
        showAlert('warning', i18n.t('pricing.login_required'));
        window.location.href = '/'; // Redirect to login
        return;
    }

    showLoading(i18n.t('pricing.calculating_renewal_date'));

    try {
        // 1. 调用新API计算续费后的到期日
        const renewalResponse = await fetch('/api/subscriptions/calculate-renewal-date', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({ subscription_type_id: planId })
        });

        if (!renewalResponse.ok) {
            throw new Error('Failed to calculate renewal date');
        }

        const renewalData = await renewalResponse.json();
        hideLoading();

        if (!renewalData.success || !renewalData.data.new_expiry_date) {
            showAlert('danger', `${i18n.t('pricing.operation_failed')}: ${renewalData.message || i18n.t('pricing.cannot_calculate_date')}`);
            return;
        }

        const newExpiryDate = renewalData.data.new_expiry_date;

        // 2. 显示包含新到期日的确认信息
        const confirmationMessage = i18n.t('pricing.renewal_confirmation', planName, newExpiryDate);
        
        const userConfirmed = confirm(confirmationMessage);
        if (!userConfirmed) {
            return; // 用户取消操作
        }

        // 3. 用户确认后，继续创建支付订单
        const isFree = price <= 0;
        const paymentType = 'alipay'; // 默认使用支付宝

        showLoading(isFree ? i18n.t('pricing.activating', planName) : i18n.t('pricing.creating_order', planName));

        const createPaymentResponse = await fetch('/api/payments/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify({
                subscription_type_id: planId,
                payment_type: paymentType
            })
        });
        
        if (handleUnauthorizedResponse(createPaymentResponse)) return;

        const data = await createPaymentResponse.json();
        
        // 从这里开始，是原有的处理支付响应的逻辑
        hideLoading();
        if (data.success) {
            const paymentData = data.data; // actual payload
            
            // 处理测试环境的自动支付响应
            if (paymentData && paymentData.type === 'debug_payment') {
                showAlert('success', i18n.t('pricing.test_payment'));
                // 直接使用返回的跳转URL
                if (paymentData.redirect_url) {
                    setTimeout(() => {
                        window.location.href = paymentData.redirect_url;
                    }, 1500);
                } else {
                    // 如果没有跳转URL，刷新订阅信息
                    loadSubscriptionInfo();
                    showSection('my-subscription');
                }
            }
            // 处理正常的支付表单响应
            else if (paymentData && paymentData.type === 'html' && paymentData.content) {
                showAlert('info', i18n.t('pricing.order_created'));
                
                // 保存支付状态到sessionStorage，防止页面刷新时丢失
                sessionStorage.setItem('payment_in_progress', 'true');
                sessionStorage.setItem('payment_order_id', paymentData.order_id || '');
                sessionStorage.setItem('payment_initiated_time', Date.now().toString());
                
                // 创建临时容器解析表单
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = paymentData.content;
                const formElement = tempDiv.querySelector('#epaySubmit');
                
                if (formElement) {
                    // 从表单中提取action和所有参数
                    const action = formElement.action;
                    const method = formElement.method || 'POST';
                    
                    // 检测是否为移动设备
                    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
                    
                    if (method.toUpperCase() === 'GET' || isMobile) {
                        // GET方法或移动设备：构建URL直接跳转（更可靠）
                        const params = new URLSearchParams();
                        const inputs = formElement.querySelectorAll('input[type="hidden"]');
                        inputs.forEach(input => {
                            params.append(input.name, input.value);
                        });
                        
                        // 构建支付URL
                        const paymentUrl = `${action}?${params.toString()}`;
                        
                        console.log('Payment URL:', paymentUrl);
                        console.log('Is Mobile:', isMobile);
                        
                        // 确保URL是绝对路径
                        if (!action.startsWith('http')) {
                            console.error('Payment URL is not absolute:', action);
                            showAlert('danger', '支付页面地址无效');
                            return;
                        }
                        
                        // 标记正在跳转支付页面，避免其他逻辑干扰
                        window.isNavigatingToPayment = true;
                        
                        // 直接跳转，不区分移动端和桌面端
                        window.location.href = paymentUrl;
                    } else {
                        // POST方法（桌面端）：创建并提交真实表单
                        // 将表单直接添加到body中（而不是在隐藏容器中）
                        formElement.style.display = 'none';
                        document.body.appendChild(formElement);
                        
                        // 给Safari一点时间来处理DOM
                        setTimeout(() => {
                            try {
                                formElement.submit();
                            } catch (e) {
                                console.error("Form submission failed:", e);
                                // 如果表单提交失败，尝试GET方式作为后备
                                const params = new URLSearchParams();
                                const inputs = formElement.querySelectorAll('input[type="hidden"]');
                                inputs.forEach(input => {
                                    params.append(input.name, input.value);
                                });
                                window.location.href = `${action}?${params.toString()}`;
                            }
                        }, 10);
                    }
                } else {
                    console.error("Unable to find payment form element.");
                    showAlert('danger', i18n.t('common.operation_canceled'));
                }
            }
            else if (paymentData && paymentData.type === 'redirect_url' && paymentData.content) {
                showAlert('info', i18n.t('pricing.order_created'));
                window.location.href = paymentData.content;
            }
            else if (paymentData && paymentData.activated_directly) {
                showAlert('success', data.message || i18n.t('pricing.activated'));
                loadSubscriptionInfo(); // Reload subscription info to reflect changes
                showSection('my-subscription'); // Navigate to subscription page
            }
            else {
                 showAlert('warning', i18n.t('pricing.operation_success') + (data.message ? ` (${data.message})` : ''));
                 loadSubscriptionInfo();
            }
         } else {
             // API returned success: false
             showAlert('danger', `${i18n.t('pricing.operation_failed')}: ${data.message || i18n.t('common.unknown_error')}`);
         }

    } catch (error) {
        hideLoading();
        console.error('Failed to create payment order:', error);
        showAlert('danger', i18n.t('common.network_error'));
    }
}

// 续费当前订阅
async function renewCurrentSubscription() {
    const token = localStorage.getItem('token');
    if (!token) {
        showAlert('warning', i18n.t('pricing.login_required'));
        window.location.href = '/';
        return;
    }

    showLoading(i18n.t('subscription.checking_current'));

    try {
        // 1. 获取当前订阅信息
        const currentSubResponse = await fetch('/api/subscriptions/current', {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        if (handleUnauthorizedResponse(currentSubResponse)) return;

        const currentSubData = await currentSubResponse.json();

        if (!currentSubData.success || !currentSubData.data || !currentSubData.data.subscription) {
            hideLoading();
            showAlert('danger', i18n.t('subscription.no_active_subscription'));
            return;
        }

        const currentSub = currentSubData.data.subscription;
        
        // 检查是否有有效的订阅类型ID
        if (!currentSub.subscription_type_id) {
            hideLoading();
            showAlert('danger', i18n.t('subscription.no_subscription_type'));
            return;
        }

        // 使用当前订阅的类型ID、价格和名称进行续费
        const planId = currentSub.subscription_type_id;
        const planName = currentSub.plan_name || i18n.t('subscription.current_plan');
        
        // 获取订阅类型的详细信息（包括价格）
        const typeResponse = await fetch('/api/subscription-types', {
            headers: { 'Authorization': `Bearer ${token}` }
        });

        if (!typeResponse.ok) {
            hideLoading();
            showAlert('danger', i18n.t('subscription.cannot_get_plan_info'));
            return;
        }

        const typeData = await typeResponse.json();
        let price = 0;
        
        if (typeData.success && typeData.data && typeData.data.types) {
            const currentType = typeData.data.types.find(type => type.id === planId);
            if (currentType) {
                price = currentType.price;
            } else {
                hideLoading();
                showAlert('danger', i18n.t('subscription.plan_not_found'));
                return;
            }
        }

        hideLoading();
        
        // 调用原有的创建支付订单函数
        await createPaymentOrder(planId, price, planName);

    } catch (error) {
        hideLoading();
        console.error('Failed to renew subscription:', error);
        showAlert('danger', i18n.t('common.network_error'));
    }
}

// Create Payment Order and Redirect (or handle payment flow)
function createPaymentOrder_original(planId, price, planName) {
    const token = localStorage.getItem('token');
    if (!token) {
        showAlert('warning', i18n.t('pricing.login_required'));
        window.location.href = '/'; // Redirect to login
        return;
    }
    
    // For free plans, confirm activation directly
    const isFree = price <= 0;
    const confirmationMessage = isFree ?
        i18n.t('pricing.confirm_activate', planName) :
        i18n.t('pricing.confirm_purchase', planName, price.toFixed(2));

    const confirmation = confirm(confirmationMessage);
    if (!confirmation) {
        return;
    }

    // 使用易支付的支付宝通道
    const paymentType = 'alipay'; // 默认使用支付宝

    showLoading(isFree ? i18n.t('pricing.activating', planName) : i18n.t('pricing.creating_order', planName));

    fetch('/api/payments/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
            subscription_type_id: planId,
            payment_type: paymentType
        })
    })
    .then(response => {
        if (handleUnauthorizedResponse(response)) return null;
        return response.json();
    })
    .then(data => {
         if (!data) return; // Handled by unauthorized check
         hideLoading();
         if (data.success) {
            const paymentData = data.data; // actual payload
            
            // 处理测试环境的自动支付响应
            if (paymentData && paymentData.type === 'debug_payment') {
                showAlert('success', i18n.t('pricing.test_payment'));
                // 直接使用返回的跳转URL
                if (paymentData.redirect_url) {
                    setTimeout(() => {
                        window.location.href = paymentData.redirect_url;
                    }, 1500);
                } else {
                    // 如果没有跳转URL，刷新订阅信息
                    loadSubscriptionInfo();
                    showSection('my-subscription');
                }
            }
            // 处理正常的支付表单响应
            else if (paymentData && paymentData.type === 'html' && paymentData.content) {
                showAlert('info', i18n.t('pricing.order_created')); 
                
                // 保存支付状态到sessionStorage，防止页面刷新时丢失
                sessionStorage.setItem('payment_in_progress', 'true');
                sessionStorage.setItem('payment_order_id', paymentData.order_id || '');
                sessionStorage.setItem('payment_initiated_time', Date.now().toString());
                
                // 创建临时容器解析表单
                const tempDiv = document.createElement('div');
                tempDiv.innerHTML = paymentData.content;
                const formElement = tempDiv.querySelector('#epaySubmit');
                
                if (formElement) {
                    // 从表单中提取action和所有参数
                    const action = formElement.action;
                    const method = formElement.method || 'POST';
                    
                    // 检测是否为移动设备
                    const isMobile = /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
                    
                    if (method.toUpperCase() === 'GET' || isMobile) {
                        // GET方法或移动设备：构建URL直接跳转（更可靠）
                        const params = new URLSearchParams();
                        const inputs = formElement.querySelectorAll('input[type="hidden"]');
                        inputs.forEach(input => {
                            params.append(input.name, input.value);
                        });
                        
                        // 构建支付URL
                        const paymentUrl = `${action}?${params.toString()}`;
                        
                        console.log('Payment URL:', paymentUrl);
                        console.log('Is Mobile:', isMobile);
                        
                        // 确保URL是绝对路径
                        if (!action.startsWith('http')) {
                            console.error('Payment URL is not absolute:', action);
                            showAlert('danger', '支付页面地址无效');
                            return;
                        }
                        
                        // 标记正在跳转支付页面，避免其他逻辑干扰
                        window.isNavigatingToPayment = true;
                        
                        // 直接跳转，不区分移动端和桌面端
                        window.location.href = paymentUrl;
                    } else {
                        // POST方法（桌面端）：创建并提交真实表单
                        // 将表单直接添加到body中（而不是在隐藏容器中）
                        formElement.style.display = 'none';
                        document.body.appendChild(formElement);
                        
                        // 给Safari一点时间来处理DOM
                        setTimeout(() => {
                            try {
                                formElement.submit();
                            } catch (e) {
                                console.error("Form submission failed:", e);
                                // 如果表单提交失败，尝试GET方式作为后备
                                const params = new URLSearchParams();
                                const inputs = formElement.querySelectorAll('input[type="hidden"]');
                                inputs.forEach(input => {
                                    params.append(input.name, input.value);
                                });
                                window.location.href = `${action}?${params.toString()}`;
                            }
                        }, 10);
                    }
                } else {
                    console.error("Unable to find payment form element.");
                    showAlert('danger', i18n.t('common.operation_canceled'));
                }
            } 
            else if (paymentData && paymentData.type === 'redirect_url' && paymentData.content) {
                showAlert('info', i18n.t('pricing.order_created')); 
                window.location.href = paymentData.content;
            }
            else if (paymentData && paymentData.activated_directly) { 
                showAlert('success', data.message || i18n.t('pricing.activated'));
                loadSubscriptionInfo(); // Reload subscription info to reflect changes
                showSection('my-subscription'); // Navigate to subscription page
            } 
            else {
                 showAlert('warning', i18n.t('pricing.operation_success') + (data.message ? ` (${data.message})` : ''));
                 loadSubscriptionInfo();
            }
         } else {
             // API returned success: false
             showAlert('danger', `${i18n.t('pricing.operation_failed')}: ${data.message || i18n.t('common.unknown_error')}`);
         }
    })
    .catch(error => {
         hideLoading();
         console.error('Failed to create payment order:', error);
         showAlert('danger', i18n.t('common.network_error'));
    });
}

// Load Devices for Management
const deviceManagementSection = document.getElementById('device-management-section');

function escapeHtml(unsafe) {
    if (unsafe === null || typeof unsafe === 'undefined') return '';
    return unsafe
         .toString()
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

function loadDevices() {
    const token = localStorage.getItem('token');
    const devicesTableBody = document.getElementById('devices-table-body');
    const noDevicesMessage = document.getElementById('no-devices-message');

    // Elements for device quota overview
    const deviceQuotaStatsEl = document.getElementById('device-quota-stats');
    const deviceQuotaProgressBarEl = document.getElementById('device-quota-progress-bar');
    const deviceQuotaMessageEl = document.getElementById('device-quota-message');

    if (!devicesTableBody || !noDevicesMessage || !deviceQuotaStatsEl || !deviceQuotaProgressBarEl || !deviceQuotaMessageEl) {
        console.error("Device management UI elements not found.");
        return;
    }

    // Show loading state for table and overview
    devicesTableBody.innerHTML = `<tr><td colspan="5" class="text-center"><div class="spinner-border spinner-border-sm" role="status"></div> ${i18n.t('device.loading')}</td></tr>`;
    noDevicesMessage.style.display = 'none';
    deviceQuotaStatsEl.textContent = '-- / --';
    deviceQuotaProgressBarEl.style.width = '0%';
    deviceQuotaProgressBarEl.setAttribute('aria-valuenow', '0');
    // Reset progress bar classes to default
    deviceQuotaProgressBarEl.className = 'progress-bar'; 
    deviceQuotaMessageEl.textContent = i18n.t('device.loading_quota');

    Promise.all([
        fetch('/api/devices', { headers: { 'Authorization': `Bearer ${token}` } }).then(res => {
            if (handleUnauthorizedResponse(res)) return null;
            return res.json();
        }),
        fetch('/api/subscriptions/current', { headers: { 'Authorization': `Bearer ${token}` } }).then(res => { // Fetch subscription info
            if (handleUnauthorizedResponse(res)) return null;
            return res.json();
        })
    ])
    .then(([devicesResponse, subscriptionResponse]) => {
        let usedDevices = 0;
        let maxDevices = 0;
        let devicesHtml = '';
        let apiErrorOccurred = false;
        let deviceApiSuccess = devicesResponse && devicesResponse.success;
        let subApiSuccess = subscriptionResponse && subscriptionResponse.success;

        // Process Device Data
        if (deviceApiSuccess && devicesResponse.data && Array.isArray(devicesResponse.data.devices)) {
            usedDevices = devicesResponse.data.devices.length;
            if (usedDevices === 0) {
                noDevicesMessage.style.display = 'block';
                devicesTableBody.innerHTML = ''; // Clear loading
            } else {
                noDevicesMessage.style.display = 'none';
                devicesResponse.data.devices.forEach(device => {
                    devicesHtml += `
                        <tr>
                            <td data-label="${i18n.t('device.name')}">${escapeHtml(device.name || i18n.t('device.unknown'))}</td>
                            <td data-label="${i18n.t('device.ip')}">${escapeHtml(device.ip_address || '-')}</td>
                            <td data-label="${i18n.t('device.type')}">${escapeHtml(device.device_type || i18n.t('device.unknown_type'))}</td>
                            <td data-label="${i18n.t('device.operation')}">
                                <button class="btn btn-sm btn-outline-danger logout-device-btn" data-device-id="${device.id}" title="${i18n.t('device.logout')}">
                                    <i class="bi bi-box-arrow-right"></i> ${i18n.t('device.logout')}
                                </button>
                            </td>
                        </tr>
                    `;
                });
                devicesTableBody.innerHTML = devicesHtml;
                document.querySelectorAll('.logout-device-btn').forEach(button => {
                    button.addEventListener('click', function() {
                        const deviceId = this.getAttribute('data-device-id');
                        handleLogoutDevice(deviceId);
                    });
                });
            }
        } else {
            apiErrorOccurred = true;
            devicesTableBody.innerHTML = `<tr><td colspan="5" class="text-danger text-center">${i18n.t('device.api_error')}: ${escapeHtml(devicesResponse ? devicesResponse.message : i18n.t('common.unknown_error'))}</td></tr>`;
            noDevicesMessage.style.display = 'none';
            usedDevices = 0; // Cannot determine
        }

        // Process Subscription Data
        const sub = (subApiSuccess && subscriptionResponse.data) ? subscriptionResponse.data.subscription : null;
        if (sub) {
            if (sub.status_text === i18n.t('status.active') && !sub.is_expired) {
                maxDevices = sub.max_devices || 0;
            } else { // "No subscription", "Expired", or other non-active states
                maxDevices = 0;
            }
        } else if (!subApiSuccess) { // Subscription API call failed
            apiErrorOccurred = true;
            maxDevices = 0; // Cannot determine
        } else { // Success but no subscription object (should not happen with current backend logic for no-subscription)
            maxDevices = 0;
        }
        
        // Update Quota Stats Text
        let statTextUsed = deviceApiSuccess ? usedDevices : i18n.t('common.loading_error');
        let statTextMax = i18n.t('common.loading_error');
        if (subApiSuccess && sub) { // sub object exists
            statTextMax = maxDevices;
        } else if (subApiSuccess && !sub) { // API success but no subscription data (e.g. user has no subscription record at all)
             statTextMax = 0; // max_devices is 0
        }
        deviceQuotaStatsEl.textContent = `${statTextUsed} / ${statTextMax}`;
        
        // Calculate Percentage for Progress Bar
        let percentage = 0;
        if (!apiErrorOccurred && maxDevices > 0) {
            percentage = Math.min((usedDevices / maxDevices) * 100, 100);
        } else if (!apiErrorOccurred && maxDevices === 0 && usedDevices > 0) { // Devices exist but no quota
            percentage = 100; // Show full bar as problematic
        } else if (apiErrorOccurred) {
            percentage = 0; // Or 100 if you want to show a full error bar
        }

        deviceQuotaProgressBarEl.style.width = `${percentage}%`;
        deviceQuotaProgressBarEl.setAttribute('aria-valuenow', percentage.toFixed(0));
        deviceQuotaProgressBarEl.className = 'progress-bar'; // Reset classes

        // Update Progress Bar Color and Message
        if (apiErrorOccurred) {
            deviceQuotaProgressBarEl.classList.add('bg-danger');
            let specificErrorMessage = i18n.t('common.loading_error') + ": ";
            if (!deviceApiSuccess && devicesResponse) specificErrorMessage += `${i18n.t('device.loading')} (${devicesResponse.message || i18n.t('common.unknown_error')}) `;
            if (!subApiSuccess && subscriptionResponse) specificErrorMessage += `${i18n.t('subscription.loading')} (${subscriptionResponse.message || i18n.t('common.unknown_error')})`;
            if (specificErrorMessage === i18n.t('common.loading_error') + ": ") specificErrorMessage = i18n.t('device.loading_quota') + ": " + i18n.t('common.unknown_error');
            deviceQuotaMessageEl.textContent = escapeHtml(specificErrorMessage.trim());
        } else if (!sub || sub.status_text === i18n.t('status.no_subscription') || sub.is_expired) { // No active subscription
            deviceQuotaProgressBarEl.classList.add(usedDevices > 0 ? 'bg-danger' : 'bg-secondary');
            if (usedDevices > 0) {
                deviceQuotaMessageEl.textContent = i18n.t('device.subscription_expired', usedDevices);
            } else {
                deviceQuotaMessageEl.textContent = sub ? (sub.status_text === i18n.t('status.no_subscription') ? i18n.t('device.no_subscription') : i18n.t('device.subscription_expired_short')) : i18n.t('common.unable_get');
            }
        } else if (usedDevices > maxDevices) { // Exceeded quota
            deviceQuotaProgressBarEl.classList.add('bg-danger');
            deviceQuotaMessageEl.textContent = i18n.t('device.quota_exceeded');
        } else if (percentage >= 80) { // Nearing quota
            deviceQuotaProgressBarEl.classList.add('bg-warning');
            deviceQuotaMessageEl.textContent = i18n.t('device.quota_warning');
        } else { // Quota OK
            deviceQuotaProgressBarEl.classList.add('bg-success');
            deviceQuotaMessageEl.textContent = i18n.t('device.quota_good');
        }
    })
    .catch(error => {
        console.error('Failed to get device or subscription info (Promise.all catch):', error);
        devicesTableBody.innerHTML = `<tr><td colspan="5" class="text-danger text-center">${i18n.t('common.loading_error')}: ${escapeHtml(error.message || i18n.t('common.server_error'))}</td></tr>`;
        noDevicesMessage.style.display = 'none';
        if(deviceQuotaStatsEl) deviceQuotaStatsEl.textContent = i18n.t('status.error');
        if(deviceQuotaProgressBarEl) {
            deviceQuotaProgressBarEl.style.width = '100%';
            deviceQuotaProgressBarEl.classList.remove('bg-success', 'bg-warning');
            deviceQuotaProgressBarEl.classList.add('bg-danger');
        }
        if(deviceQuotaMessageEl) deviceQuotaMessageEl.textContent = i18n.t('device.api_error');
    });
}

function handleLogoutDevice(deviceId) {
    const confirmation = confirm(i18n.t('device.logout_confirm'));
    if (!confirmation) {
        return;
    }

    showLoading(i18n.t('device.logout_processing'));
    const token = localStorage.getItem('token');

    fetch(`/api/devices/${deviceId}/logout`, {
        method: 'POST',
        headers: { 'Authorization': `Bearer ${token}` }
    })
    .then(response => {
        if (handleUnauthorizedResponse(response)) return null;
        return response.json();
    })
    .then(data => {
        if (!data) return; // Handled by unauthorized check
        hideLoading();
        if (data.success) {
            showAlert('success', data.message || i18n.t('device.logout_success'));
            loadDevices(); // Refresh the device list
            // 如果在dashboard页面，也刷新设备额度显示
            if (document.getElementById('dashboard-section').style.display !== 'none') {
                setTimeout(() => {
                    refreshDeviceQuota();
                }, 500);
            }
        } else {
            showAlert('danger', `${i18n.t('device.logout_failed')}: ${data.message || i18n.t('common.unknown_error')}`);
        }
    })
    .catch(error => {
        hideLoading();
        console.error('Failed to logout device:', error);
        showAlert('danger', i18n.t('device.network_error'));
    });
}

// Language switcher setup
function setupLanguageSwitcher() {
    // Update current language display
    const currentLangDisplay = document.getElementById('current-language');
    if (currentLangDisplay) {
        currentLangDisplay.textContent = i18n.getCurrentLanguage() === 'zh-CN' ? i18n.t('lang.zh_CN') : 'English';
    }
    
    // Setup language option click handlers
    document.querySelectorAll('.language-option').forEach(option => {
        option.addEventListener('click', (e) => {
            e.preventDefault();
            const lang = e.target.getAttribute('data-lang');
            if (lang && i18n.setLanguage(lang)) {
                // Update current language display
                if (currentLangDisplay) {
                    currentLangDisplay.textContent = lang === 'zh-CN' ? i18n.t('lang.zh_CN') : 'English';
                }
                // Reload subscription info to update dynamic content
                loadSubscriptionInfo();
                // Re-display user info since i18n update overwrites it
                const user = JSON.parse(localStorage.getItem('user') || '{}');
                const userDisplay = document.getElementById('user-display');
                const dropdownUsername = document.getElementById('dropdown-username');
                const dropdownEmail = document.getElementById('dropdown-email');
                
                if (userDisplay && user) {
                    userDisplay.textContent = user.email || user.username || i18n.t('user.loading');
                }
                if (dropdownUsername && user) {
                    dropdownUsername.textContent = user.username || user.email || i18n.t('user.loading');
                }
                if (dropdownEmail && user) {
                    dropdownEmail.textContent = user.email || '--';
                }
                // Update direct login UI to reflect language change
                updateUIForDirectLogin();
                // Reload devices if device management section is active
                const deviceSection = document.getElementById('device-management-section');
                if (deviceSection && deviceSection.style.display !== 'none') {
                    loadDevices();
                }
                // Reload pricing plans if pricing section is active
                const pricingSection = document.getElementById('pricing-section');
                if (pricingSection && pricingSection.style.display !== 'none') {
                    loadSubscriptionPlans();
                }
                // Update section title if needed
                const currentSection = document.querySelector('.section-content:not([style*="display: none"])');
                if (currentSection) {
                    const sectionId = currentSection.id.replace('-section', '');
                    const activeLink = document.querySelector(`.sidebar .nav-link[data-section="${sectionId}"]`);
                    if (activeLink) {
                        const i18nKey = activeLink.querySelector('[data-i18n]')?.getAttribute('data-i18n');
                        if (i18nKey) {
                            topbarTitle.textContent = i18n.t(i18nKey);
                        }
                    }
                }
            }
        });
    });
    
    // Listen for language change events
    window.addEventListener('languageChanged', (e) => {
        // Update any dynamic content that needs to be re-rendered
        console.log('Language changed to:', e.detail.language);
    });
}
